<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>%VITE_APP_TITLE%</title>
  </head>
  <body>
    <div id="app">
      <!-- 原始加载效果样式备份
      <style>
        .app-loading {
          display: flex;
          width: 100%;
          height: 100%;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          background: #f0f2f5;
        }
      -->
      <style>
        .app-loading {
          display: flex;
          width: 100%;
          height: 100vh;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
          overflow: hidden;
        }

        .app-loading .app-loading-wrap {
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          z-index: 1;
        }

        .app-loading .app-loading-title {
          margin-bottom: 40px;
          font-size: 24px;
          font-weight: 600;
          text-align: center;
          background: linear-gradient(45deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          opacity: 0;
          animation: titleFadeIn 1s ease-out 0.5s forwards;
          letter-spacing: 1px;
        }

        .app-loading .app-loading-spinner {
          position: relative;
          width: 120px;
          height: 120px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .app-loading .spinner-ring {
          position: absolute;
          border-radius: 50%;
          border: 3px solid transparent;
          will-change: transform, opacity;
        }

        .app-loading .spinner-ring:nth-child(1) {
          width: 120px;
          height: 120px;
          border-top: 3px solid #667eea;
          border-right: 3px solid #667eea;
          animation: ringPulse 2.5s ease-in-out infinite, ringRotate 8s linear infinite;
          box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }

        .app-loading .spinner-ring:nth-child(2) {
          width: 90px;
          height: 90px;
          border-bottom: 3px solid #764ba2;
          border-left: 3px solid #764ba2;
          animation: ringPulse 2s ease-in-out infinite 0.3s, ringRotate 6s linear infinite reverse;
          box-shadow: 0 0 15px rgba(118, 75, 162, 0.3);
        }

        .app-loading .spinner-ring:nth-child(3) {
          width: 60px;
          height: 60px;
          border-top: 3px solid #f093fb;
          border-right: 3px solid #f093fb;
          animation: ringPulse 1.5s ease-in-out infinite 0.6s, ringRotate 4s linear infinite;
          box-shadow: 0 0 10px rgba(240, 147, 251, 0.3);
        }

        @keyframes ringPulse {
          0%, 100% {
            transform: scale(1);
            opacity: 1;
          }
          50% {
            transform: scale(1.1);
            opacity: 0.8;
          }
        }

        @keyframes ringRotate {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }

        @keyframes titleFadeIn {
          0% {
            opacity: 0;
            transform: translateY(20px);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }

        /* 添加背景粒子效果 */
        .app-loading::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-image: 
            radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(240, 147, 251, 0.1) 0%, transparent 50%);
          animation: backgroundShift 10s ease-in-out infinite;
        }

        @keyframes backgroundShift {
          0%, 100% {
            opacity: 0.3;
            transform: scale(1);
          }
          50% {
            opacity: 0.6;
            transform: scale(1.05);
          }
        }
      </style>
      <div class="app-loading">
        <div class="app-loading-wrap">
          <div class="app-loading-title">%VITE_APP_TITLE%</div>
          <div class="app-loading-spinner">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
          </div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
