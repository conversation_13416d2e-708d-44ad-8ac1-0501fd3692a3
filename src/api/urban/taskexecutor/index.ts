import request from '@/config/axios'

// 任务执行人 VO
export interface TaskExecutorVO {
  id: number // 主键
  taskId: string // 任务ID
  userId: number // 用户ID
  userType: string // 用户类型：调查员|其他
}

// 任务执行人 API
export const TaskExecutorApi = {
  // 查询任务执行人分页
  getTaskExecutorPage: async (params: any) => {
    return await request.get({ url: `/urban/task-executor/page`, params })
  },

  // 查询任务执行人详情
  getTaskExecutor: async (id: number) => {
    return await request.get({ url: `/urban/task-executor/get?id=` + id })
  },

  // 新增任务执行人
  createTaskExecutor: async (data: TaskExecutorVO) => {
    return await request.post({ url: `/urban/task-executor/create`, data })
  },

  // 修改任务执行人
  updateTaskExecutor: async (data: TaskExecutorVO) => {
    return await request.put({ url: `/urban/task-executor/update`, data })
  },

  // 删除任务执行人
  deleteTaskExecutor: async (id: number) => {
    return await request.delete({ url: `/urban/task-executor/delete?id=` + id })
  },

  // 导出任务执行人 Excel
  exportTaskExecutor: async (params) => {
    return await request.download({ url: `/urban/task-executor/export-excel`, params })
  }
}