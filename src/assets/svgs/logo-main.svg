<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="cityGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:var(--logo-primary-color, #409EFF);stop-opacity:1" />
      <stop offset="100%" style="stop-color:var(--logo-secondary-color, #67C23A);stop-opacity:0.8" />
    </linearGradient>
    
    <!-- 心跳线渐变 -->
    <linearGradient id="pulseGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:var(--logo-accent-color, #E6A23C);stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:var(--logo-accent-color, #E6A23C);stop-opacity:1" />
      <stop offset="100%" style="stop-color:var(--logo-accent-color, #E6A23C);stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="30" fill="var(--logo-bg-color, rgba(255,255,255,0.1))" 
          stroke="var(--logo-border-color, rgba(64,158,255,0.3))" stroke-width="1"/>
  
  <!-- 城市建筑群 -->
  <g fill="url(#cityGradient)" stroke="var(--logo-stroke-color, rgba(255,255,255,0.2))" stroke-width="0.5">
    <!-- 建筑1 -->
    <rect x="8" y="35" width="6" height="20" rx="1"/>
    <!-- 建筑2 -->
    <rect x="16" y="28" width="8" height="27" rx="1"/>
    <!-- 建筑3 -->
    <rect x="26" y="32" width="6" height="23" rx="1"/>
    <!-- 建筑4 -->
    <rect x="34" y="25" width="10" height="30" rx="1"/>
    <!-- 建筑5 -->
    <rect x="46" y="30" width="7" height="25" rx="1"/>
    <!-- 建筑6 -->
    <rect x="55" y="38" width="5" height="17" rx="1"/>
  </g>
  
  <!-- 建筑窗户细节 -->
  <g fill="var(--logo-window-color, rgba(255,255,255,0.6))">
    <!-- 建筑2的窗户 -->
    <rect x="18" y="32" width="1.5" height="1.5" rx="0.2"/>
    <rect x="21" y="32" width="1.5" height="1.5" rx="0.2"/>
    <rect x="18" y="36" width="1.5" height="1.5" rx="0.2"/>
    <rect x="21" y="36" width="1.5" height="1.5" rx="0.2"/>
    
    <!-- 建筑4的窗户 -->
    <rect x="36" y="29" width="1.5" height="1.5" rx="0.2"/>
    <rect x="39" y="29" width="1.5" height="1.5" rx="0.2"/>
    <rect x="41" y="29" width="1.5" height="1.5" rx="0.2"/>
    <rect x="36" y="33" width="1.5" height="1.5" rx="0.2"/>
    <rect x="39" y="33" width="1.5" height="1.5" rx="0.2"/>
    <rect x="41" y="33" width="1.5" height="1.5" rx="0.2"/>
    
    <!-- 建筑5的窗户 -->
    <rect x="48" y="34" width="1.5" height="1.5" rx="0.2"/>
    <rect x="50.5" y="34" width="1.5" height="1.5" rx="0.2"/>
    <rect x="48" y="38" width="1.5" height="1.5" rx="0.2"/>
    <rect x="50.5" y="38" width="1.5" height="1.5" rx="0.2"/>
  </g>
  
  <!-- 心跳线 -->
  <path d="M 5 20 L 12 20 L 15 12 L 18 28 L 22 8 L 26 32 L 30 20 L 35 20 L 38 15 L 42 25 L 45 20 L 50 20 L 53 16 L 56 24 L 59 20" 
        fill="none" 
        stroke="url(#pulseGradient)" 
        stroke-width="2" 
        stroke-linecap="round" 
        stroke-linejoin="round">
    <!-- 心跳动画 -->
    <animate attributeName="stroke-dasharray" 
             values="0,100;50,50;100,0;0,100" 
             dur="3s" 
             repeatCount="indefinite"/>
  </path>
  
  <!-- 数据点 -->
  <g fill="var(--logo-accent-color, #E6A23C)">
    <circle cx="15" cy="12" r="1.5" opacity="0.8">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="22" cy="8" r="1.5" opacity="0.8">
      <animate attributeName="opacity" values="1;0.8;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="38" cy="15" r="1.5" opacity="0.8">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite" begin="0.5s"/>
    </circle>
    <circle cx="53" cy="16" r="1.5" opacity="0.8">
      <animate attributeName="opacity" values="1;0.8;1" dur="2s" repeatCount="indefinite" begin="1s"/>
    </circle>
  </g>
  
  <!-- 医疗十字标识 -->
  <g fill="var(--logo-medical-color, #67C23A)" opacity="0.9">
    <rect x="30" y="10" width="4" height="2" rx="0.5"/>
    <rect x="31" y="9" width="2" height="4" rx="0.5"/>
  </g>
</svg> 