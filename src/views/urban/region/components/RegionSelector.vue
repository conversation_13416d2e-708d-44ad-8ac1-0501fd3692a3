<template>
  <el-tree-select
    v-model="selectedRegion"
    :data="regionTree"
    :props="{ label: 'name', value: 'id', isLeaf: 'leaf' }"
    :placeholder="placeholder"
    class="w-full"
    clearable
    node-key="id"
    :load="loadRegionTree"
    lazy
    check-strictly
    :default-expanded-keys="expandedKeys"
    filterable
    :filter-method="filterRegionNode"
    @change="handleRegionChange"
  />
</template>

<script setup lang="ts">
import { ref,  watch, onMounted, defineProps, defineEmits } from 'vue'
import { RegionApi } from '@/api/urban/region'

const props = defineProps({
  // 默认选中的区域ID
  modelValue: {
    type: [String, Number],
    default: ''
  },
  // 占位符文本
  placeholder: {
    type: String,
    default: '请选择行政区划'
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 根节点ID，默认为全国
  rootId: {
    type: String,
    default: '-99'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 选中的区域ID
const selectedRegion = ref(props.modelValue)

// 监听外部传入的值变化
watch(() => props.modelValue, (newVal) => {
  selectedRegion.value = newVal
})

// 监听内部选择的值变化
watch(() => selectedRegion.value, (newVal) => {
  emit('update:modelValue', newVal)
})

/** 树形选择器数据 */
const regionTree = ref([])
// 已加载的节点缓存，防止重复加载或循环依赖
const treeLoadedCache = ref(new Map())
// 父节点路径缓存，用于编辑时展开到选中节点
const parentPathCache = ref(new Map())
// 默认展开的节点
const expandedKeys = ref([])

// 组件初始化
onMounted(async () => {
  // 初始化根节点数据
  await loadRootRegions()

  // 如果有初始值，加载并展开到选中节点
  if (selectedRegion.value) {
    await loadSelectedRegionPath(selectedRegion.value)
  }
})

/** 加载根节点区域数据 */
const loadRootRegions = async () => {
  try {
    const data = await RegionApi.getRegionList({ parentId: props.rootId })
    // 只保留省级节点 (regionalLevel = 3)
    const provinceNodes = data.filter(item => item.regionalLevel === 3)
    regionTree.value = provinceNodes.map(item => ({
      ...item,
      leaf: false // 省级节点不是叶子节点
    }))

    // 缓存根节点数据
    treeLoadedCache.value.set(props.rootId, regionTree.value)
  } catch (error) {
    console.error('加载区域树失败', error)
  }
}

/** 懒加载子节点 */
const loadRegionTree = async (node, resolve) => {
  try {
    const parentId = node.data?.id || props.rootId
    const currentLevel = node.data?.regionalLevel || 0

    // 检查缓存中是否已有数据
    if (treeLoadedCache.value.has(parentId)) {
      resolve(treeLoadedCache.value.get(parentId))
      return
    }

    // 加载新数据
    const data = await RegionApi.getRegionList({ parentId })
    let nodes = []

    if (currentLevel === 3) {
      // 如果当前是省级，只加载市级节点 (regionLevel = 4)
      nodes = data
        .filter(item => item.regionLevel === 4)
        .map(item => ({
          ...item,
          leaf: false // 市级节点不是叶子节点
        }))
    } else if (currentLevel === 4) {
      // 如果当前是市级，只加载县级节点 (regionLevel = 5)
      nodes = data
        .filter(item => item.regionLevel === 5)
        .map(item => ({
          ...item,
          leaf: true // 县级节点是叶子节点
        }))
    } else {
      // 其他情况不应该发生，但为了健壮性，保留处理
      nodes = data.map(item => ({
        ...item,
        leaf: item.regionLevel >= 5 // 县级及以上是叶子节点
      }))
    }

    // 更新缓存
    treeLoadedCache.value.set(parentId, nodes)
    resolve(nodes)
  } catch (error) {
    console.error('加载子节点失败', error)
    resolve([])
  }
}

/** 加载选中节点的路径并展开 */
const loadSelectedRegionPath = async (id) => {
  try {
    // 获取节点路径
    const pathData = await RegionApi.getRegionList(id)

    // 设置展开的节点
    expandedKeys.value = pathData.map(item => item.id)

    // 缓存路径信息
    pathData.forEach(item => {
      parentPathCache.value.set(item.id, item)
    })

    // 预加载路径上的节点
    for (let i = 0; i < pathData.length - 1; i++) {
      const parentId = pathData[i].id
      if (!treeLoadedCache.value.has(parentId)) {
        const children = await RegionApi.getRegionList({ parentId })
        let filteredChildren = children

        // 根据当前节点级别过滤子节点
        if (pathData[i].regionLevel === 3) {
          // 省级节点，只保留市级子节点
          filteredChildren = children.filter(item => item.regionLevel === 4)
        } else if (pathData[i].regionLevel === 4) {
          // 市级节点，只保留县级子节点
          filteredChildren = children.filter(item => item.regionLevel === 5)
        }

        treeLoadedCache.value.set(parentId, filteredChildren.map(item => ({
          ...item,
          leaf: item.regionLevel === 5 // 县级节点是叶子节点
        })))
      }
    }
  } catch (error) {
    console.error('加载选中节点路径失败', error)
  }
}

/** 过滤节点方法 */
const filterRegionNode = (value, data) => {
  if (!value) return true
  return data.name.includes(value) ||
    data.code.includes(value) ||
    (data.regionFullName && data.regionFullName.includes(value))
}

/** 处理区域选择变化 */
const handleRegionChange = async (value) => {
  if (!value) {
    emit('change', null)
    return
  }

  try {
    // 从缓存中查找或从API获取完整的区域信息
    let regionInfo = null

    // 先从缓存中查找
    for (const nodes of treeLoadedCache.value.values()) {
      const found = nodes.find(node => node.id === value)
      if (found) {
        regionInfo = found
        break
      }
    }

    // 如果缓存中没有，从API获取
    if (!regionInfo) {
      regionInfo = await RegionApi.getRegion(value)
    }

    // 检查选中的区域是否是县级 (regionLevel = 5)
    if (regionInfo.regionLevel !== 5) {
      // 如果不是县级，清空选择
      selectedRegion.value = ''
      emit('change', null)
      // 可以添加提示
      // message.warning('请选择县级行政区划')
      return
    }

    // 获取完整的省市县三级数据
    const completeData = await getCompleteRegionData(regionInfo)
    emit('change', completeData)
  } catch (error) {
    console.error('获取区域信息失败', error)
    emit('change', { id: value })
  }
}

/** 获取完整的省市县三级数据 */
const getCompleteRegionData = async (countyRegion) => {
  // 县级数据
  const county = {
    id: countyRegion.id,
    name: countyRegion.name,
    code: countyRegion.code,
    regionLevel: countyRegion.regionLevel
  }

  // 获取市级数据
  let city = null
  if (countyRegion.parentId) {
    city = await RegionApi.getRegion(countyRegion.parentId)
    city = {
      parentId: city.parentId,
      id: city.id,
      name: city.name,
      code: city.code,
      regionLevel: city.regionLevel
    }
  }

  // 获取省级数据
  let province = null
  if (city && city.parentId) {
    province = await RegionApi.getRegion(city.parentId)
    province = {
      id: province.id,
      name: province.name,
      code: province.code,
      regionLevel: province.regionLevel
    }
  }

  // 返回完整的三级数据
  return {
    province,
    city,
    county,
    // 选中的节点ID (县级)
    selectedId: county.id
  }
}
</script>
