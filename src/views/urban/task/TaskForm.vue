<template>
  <Dialog :title="dialogTitle"  v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="任务编号" prop="taskId">
            <el-input 
              v-model="formData.taskId" 
              placeholder="任务编号将自动生成" 
              readonly 
              disabled
              style="cursor: not-allowed;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务类型" prop="taskType">
            <el-select 
              v-model="formData.taskType" 
              placeholder="请选择任务类型" 
              class="!w-full"
              @change="handleTaskTypeChange"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.UC_TASK_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="任务名称" prop="taskName">
            <el-input v-model="formData.taskName" placeholder="请输入任务名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="省" prop="province">
            <el-input v-model="formData.province" placeholder="请输入省" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="市" prop="city">
            <el-input v-model="formData.city" placeholder="请输入市" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="县区" prop="xzqdm">
            <el-input v-model="formData.xzqdm" placeholder="请输入县区" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="镇街" prop="town">
            <el-input v-model="formData.town" placeholder="请输入镇街" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="社区村" prop="village">
            <el-input v-model="formData.village" placeholder="请输入社区村" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="小区" prop="community">
            <el-input v-model="formData.community" placeholder="请输入小区" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="组长" prop="leaderId">
            <UserSearchSelect
              v-model="formData.leaderId"
              placeholder="请搜索选择组长"
              @change="handleLeaderChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.UC_TASK_STATUS)"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="调查人员" prop="surveyorIds">
            <div class="surveyor-selection">
              <div v-if="selectedSurveyors.length > 0" class="selected-users">
                <el-tag
                  v-for="user in selectedSurveyors"
                  :key="user.id"
                  closable
                  @close="removeSurveyor(user.id)"
                  class="mr-2 mb-2"
                >
                  {{ user.nickname }}
                </el-tag>
              </div>
              <el-button type="primary" @click="openSurveyorSelect" :disabled="formLoading">
                <Icon icon="ep:plus" class="mr-2" />
                {{ selectedSurveyors.length > 0 ? '修改调查人员' : '选择调查人员' }}
              </el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="计划完成时间" prop="plannedTime">
            <el-date-picker
              v-model="formData.plannedTime"
              type="date"
              value-format="x"
              placeholder="选择计划完成时间"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际完成时间" prop="actualDate">
            <el-date-picker
              v-model="formData.actualDate"
              type="date"
              value-format="x"
              placeholder="选择实际完成时间"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="任务区域" prop="geom">
            <div class="map-draw-trigger" @click="openMapDrawDialog">
              <div class="map-draw-status">
                <div class="status-content">
                  <Icon 
                    :icon="hasGeomData ? 'ep:success-filled' : 'ep:warning-filled'" 
                    :class="hasGeomData ? 'text-green-500' : 'text-orange-500'"
                    class="text-lg mr-2"
                  />
                  <div class="status-text">
                    <div class="status-title">
                      {{ hasGeomData ? '已绘制任务区域' : '未绘制任务区域' }}
                    </div>
                    <div class="status-desc">
                      {{ hasGeomData ? '点击查看或重新绘制区域范围' : '点击在地图上绘制任务区域范围' }}
                    </div>
                  </div>
                </div>
                <div class="action-buttons">
                  <el-button type="primary" size="small">
                    <Icon icon="ep:edit" class="mr-1" />
                    {{ hasGeomData ? '重新绘制' : '开始绘制' }}
                  </el-button>
                  <el-button 
                    v-if="hasGeomData" 
                    type="danger" 
                    size="small" 
                    @click.stop="clearGeomData"
                  >
                    <Icon icon="ep:delete" class="mr-1" />
                    清除
                  </el-button>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <!-- 调查人员选择弹窗 -->
  <UserSelectForm ref="userSelectFormRef" @confirm="handleSurveyorConfirm" />

  <!-- 地图绘制弹窗 -->
  <MapDrawDialog
    v-model:visible="mapDrawDialogVisible"
    v-model="currentGeomData"
    title="绘制任务区域"
    :width="'85%'"
    :height="'75%'"
    :map-height="'500px'"
    :show-toolbar="true"
    :allowed-modes="[DrawMode.POLYGON]"
    :allow-multiple="false"
    :center="mapCenter"
    :zoom="13"
    :projection="'EPSG:4326'"
    :tianditu-token="'4989e906aa138e5bb1b49a3eb83a6128'"
    confirm-button-text="确认绘制"
    cancel-button-text="取消"
    @confirm="handleMapDrawConfirm"
    @cancel="handleMapDrawCancel"
  />
</template>

<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { TaskApi, TaskVO } from '@/api/urban/task'
import * as UserApi from '@/api/system/user'
import UserSearchSelect from '@/components/UserSearchSelect/index.vue'
import UserSelectForm from '@/components/UserSelectForm/index.vue'
import MapDrawDialog from '@/components/MapDraw/src/MapDrawDialog.vue'
import { DrawMode } from '@/components/MapDraw/src/types'

interface SurveyorUser {
  id: number
  nickname: string
}

/** 任务管理 表单 */
defineOptions({ name: 'TaskForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const selectedSurveyors = ref<SurveyorUser[]>([]) // 选中的调查人员
const userSelectFormRef = ref() // 用户选择表单引用

// 地图相关
const mapDrawDialogVisible = ref(false) // 地图绘制弹窗显示状态
const currentGeomData = ref('') // 当前地图数据
const mapCenter = ref<[number, number]>([129.5040, 42.9156]) // 地图中心点（延吉市）

const formData = ref({
  id: undefined,
  taskId: '' as string | undefined,
  taskType: undefined,
  taskName: undefined,
  province: undefined,
  city: undefined,
  xzqdm: undefined,
  town: undefined,
  village: undefined,
  community: undefined,
  leaderId: undefined,
  leaderName: undefined,
  surveyorIds: [] as number[], // 调查人员ID数组
  status: undefined,
  plannedTime: undefined,
  actualDate: undefined,
  geom: '' as string | undefined
})

const formRules = reactive({
  taskId: [{ required: true, message: '任务编号不能为空', trigger: 'blur' }],
  taskType: [{ required: true, message: '任务类型不能为空', trigger: 'change' }],
  taskName: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
  province: [{ required: true, message: '省不能为空', trigger: 'blur' }],
  city: [{ required: true, message: '市不能为空', trigger: 'blur' }],
  xzqdm: [{ required: true, message: '县区不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  leaderId: [{ required: true, message: '组长不能为空', trigger: 'change' }],
  geom: [{ required: true, message: '任务区域不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

// 计算属性
const hasGeomData = computed(() => {
  return formData.value.geom && formData.value.geom.length > 0
})

/** 生成任务编号 */
const generateTaskId = (taskType: string) => {
  if (!taskType) return ''
  const timestamp = Date.now()
  return `${taskType}${timestamp}`
}

/** 处理任务类型变化 */
const handleTaskTypeChange = (value: string) => {
  if (value && formType.value === 'create') {
    // 只在新增时自动生成任务编号
    formData.value.taskId = generateTaskId(value)
  }
}

/** 处理组长选择变化 */
const handleLeaderChange = (user: any) => {
  if (user) {
    formData.value.leaderName = user.nickname
  } else {
    formData.value.leaderName = undefined
  }
}

/** 打开调查人员选择弹窗 */
const openSurveyorSelect = () => {
  userSelectFormRef.value?.open(1, selectedSurveyors.value)
}

/** 处理调查人员选择确认 */
const handleSurveyorConfirm = (activityId: number, userList: SurveyorUser[]) => {
  selectedSurveyors.value = userList
  formData.value.surveyorIds = userList.map(user => user.id)
}

/** 移除调查人员 */
const removeSurveyor = (userId: number) => {
  selectedSurveyors.value = selectedSurveyors.value.filter(user => user.id !== userId)
  formData.value.surveyorIds = selectedSurveyors.value.map(user => user.id)
}

/** 打开地图绘制弹窗 */
const openMapDrawDialog = () => {
  currentGeomData.value = formData.value.geom || ''
  mapDrawDialogVisible.value = true
}

/** 处理地图绘制确认 */
const handleMapDrawConfirm = (wkt: string) => {
  formData.value.geom = wkt
  mapDrawDialogVisible.value = false
  message.success('任务区域绘制完成')
}

/** 处理地图绘制取消 */
const handleMapDrawCancel = () => {
  mapDrawDialogVisible.value = false
}

/** 清除地图数据 */
const clearGeomData = () => {
  formData.value.geom = ''
  message.info('已清除任务区域数据')
}

/** 根据用户ID获取用户信息 */
const getUserInfo = async (userId: number) => {
  try {
    const user = await UserApi.getUser(userId)
    return {
      id: user.id,
      nickname: user.nickname || user.username || `用户${user.id}`
    }
  } catch (error) {
    console.warn(`获取用户${userId}信息失败:`, error)
    return {
      id: userId,
      nickname: `用户${userId}`
    }
  }
}

/** 批量获取用户信息 */
const getUsersInfo = async (userIds: number[]) => {
  const promises = userIds.map(id => getUserInfo(id))
  return await Promise.all(promises)
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = '任务分配'
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await TaskApi.getTask(id)
      formData.value = data
      
      // 如果有调查人员ID数据，获取用户详情
      if (data.surveyorIds && data.surveyorIds.length > 0) {
        selectedSurveyors.value = await getUsersInfo(data.surveyorIds)
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TaskVO
    if (formType.value === 'create') {
      await TaskApi.createTask(data)
      message.success(t('common.createSuccess'))
    } else {
      await TaskApi.updateTask(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    taskId: '',
    taskType: undefined,
    taskName: undefined,
    province: undefined,
    city: undefined,
    xzqdm: undefined,
    town: undefined,
    village: undefined,
    community: undefined,
    leaderId: undefined,
    leaderName: undefined,
    surveyorIds: [],
    status: undefined,
    plannedTime: undefined,
    actualDate: undefined,
    geom: ''
  }
  selectedSurveyors.value = []
  currentGeomData.value = ''
  formRef.value?.resetFields()
}
</script>

<style scoped>
.surveyor-selection {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
}

.selected-users {
  margin-bottom: 12px;
}

.selected-users:last-child {
  margin-bottom: 0;
}

.map-draw-trigger {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.map-draw-trigger:hover {
  border-color: #409eff;
  background-color: #f0f8ff;
}

.map-draw-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.status-text {
  flex: 1;
}

.status-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.status-desc {
  font-size: 12px;
  color: #909399;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .el-button {
  margin: 0;
}
</style>
