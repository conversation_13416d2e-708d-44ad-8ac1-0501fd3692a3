<template>
  <div class="p-4">
    <h2 class="mb-4">用户搜索选择组件示例</h2>
    
    <el-form :model="form" label-width="120px">
      <el-form-item label="选择用户">
        <UserSearchSelect
          v-model="form.userId"
          placeholder="请输入用户名搜索"
          @change="handleUserChange"
        />
      </el-form-item>
      
      <el-form-item v-if="selectedUser">
        <el-descriptions title="已选择用户信息" :column="1" border>
          <el-descriptions-item label="用户ID">{{ selectedUser.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ selectedUser.username }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{ selectedUser.nickname }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ selectedUser.mobile }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ selectedUser.email }}</el-descriptions-item>
        </el-descriptions>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import UserSearchSelect from './index.vue'
import type { UserVO } from '@/api/system/user'

defineOptions({ name: 'UserSearchSelectExample' })

const form = reactive({
  userId: undefined
})

const selectedUser = ref<UserVO>()

const handleUserChange = (user: UserVO) => {
  selectedUser.value = user
  console.log('Selected user:', user)
}
</script> 