<template>
  <el-select
    v-model="localValue"
    filterable
    remote
    clearable
    multiple
    collapse-tags
    collapse-tags-tooltip
    :placeholder="placeholder"
    :loading="loading"
    :remote-method="remoteSearch"
    @change="handleChange"
    :class="customClass"
  >
    <el-option
      v-for="item in userOptions"
      :key="item.id"
      :label="item.nickname"
      :value="item.id"
    />
  </el-select>
</template>

<script lang="ts" setup>
import * as UserApi from '@/api/system/user'
import { PropType } from 'vue'

defineOptions({ name: 'UserSearchSelectMultiple' })

const props = defineProps({
  modelValue: {
    type: Array as PropType<number[]>,
    default: () => []
  },
  placeholder: {
    type: String,
    default: '请搜索选择多个用户'
  },
  customClass: {
    type: String,
    default: '!w-240px'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const loading = ref(false)
const userOptions = ref<UserApi.UserVO[]>([])
const userList = ref<UserApi.UserVO[]>([])
const localValue = ref<number[]>(props.modelValue as number[] || [])

// 监听modelValue变化，同步到localValue
watch(() => props.modelValue, (newVal) => {
  if (Array.isArray(newVal)) {
    localValue.value = [...newVal] as number[]
  } else {
    localValue.value = []
  }
}, { deep: true })

// 初始加载所有用户
const loadUsers = async () => {
  loading.value = true
  try {
    userList.value = await UserApi.getSimpleUserList()
    userOptions.value = [...userList.value]
  } finally {
    loading.value = false
  }
}

// 远程搜索方法
const remoteSearch = (query: string) => {
  if (query) {
    loading.value = true
    try {
      userOptions.value = userList.value.filter(
        (user) => user.nickname.toLowerCase().includes(query.toLowerCase())
      )
    } finally {
      loading.value = false
    }
  } else {
    userOptions.value = [...userList.value]
  }
}

// 处理选择变化
const handleChange = (value: number[]) => {
  localValue.value = [...value]
  emit('update:modelValue', value)
  const selectedUsers = userList.value.filter(user => value.includes(user.id))
  emit('change', selectedUsers)
}

onMounted(() => {
  loadUsers()
})
</script> 