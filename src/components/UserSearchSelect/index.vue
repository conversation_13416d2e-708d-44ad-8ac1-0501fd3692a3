<template>
  <el-select
    v-model="localValue"
    filterable
    remote
    clearable
    :placeholder="placeholder"
    :loading="loading"
    :remote-method="remoteSearch"
    @change="handleChange"
    class="!w-240px"
  >
    <el-option
      v-for="item in userOptions"
      :key="item.id"
      :label="item.nickname"
      :value="item.id"
    />
  </el-select>
</template>

<script lang="ts" setup>
import * as UserApi from '@/api/system/user'

defineOptions({ name: 'UserSearchSelect' })

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: undefined
  },
  placeholder: {
    type: String,
    default: '请搜索选择用户'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const loading = ref(false)
const userOptions = ref<UserApi.UserVO[]>([])
const userList = ref<UserApi.UserVO[]>([])
const localValue = ref(props.modelValue)
const usersLoaded = ref(false) // 用户数据加载状态

// 根据用户ID获取用户信息（fallback方案）
const getUserById = async (userId: number): Promise<UserApi.UserVO | null> => {
  try {
    const user = await UserApi.getUser(userId)
    return user
  } catch (error) {
    console.warn(`获取用户${userId}信息失败:`, error)
    return null
  }
}

// 处理modelValue回显
const handleModelValue = async (value: number | string | undefined) => {
  if (!value) {
    localValue.value = value
    return
  }

  const userId = Number(value)
  localValue.value = userId

  // 如果用户数据已加载，检查用户是否在列表中
  if (usersLoaded.value) {
    const existingUser = userList.value.find(user => user.id === userId)
    if (!existingUser) {
      // 如果用户不在列表中，尝试获取用户信息并添加到列表
      const user = await getUserById(userId)
      if (user) {
        userList.value.unshift(user)
        userOptions.value.unshift(user)
      }
    }
  }
}

// 监听modelValue变化，同步到localValue
watch(() => props.modelValue, (newVal) => {
  handleModelValue(newVal)
}, { immediate: true })

// 初始加载所有用户
const loadUsers = async () => {
  loading.value = true
  try {
    userList.value = await UserApi.getSimpleUserList()
    userOptions.value = [...userList.value]
    usersLoaded.value = true
    
    // 用户数据加载完成后，重新处理modelValue
    if (props.modelValue) {
      await handleModelValue(props.modelValue)
    }
  } finally {
    loading.value = false
  }
}

// 远程搜索方法
const remoteSearch = (query: string) => {
  if (query) {
    loading.value = true
    try {
      userOptions.value = userList.value.filter(
        (user) => user.nickname.toLowerCase().includes(query.toLowerCase())
      )
    } finally {
      loading.value = false
    }
  } else {
    userOptions.value = [...userList.value]
  }
}

// 处理选择变化
const handleChange = (value: number) => {
  localValue.value = value
  emit('update:modelValue', value)
  const selectedUser = userList.value.find(user => user.id === value)
  emit('change', selectedUser)
}

onMounted(() => {
  loadUsers()
})
</script> 