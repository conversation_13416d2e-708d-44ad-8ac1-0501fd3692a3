<template>
  <div class="p-4">
    <h2 class="mb-4">多用户搜索选择组件示例</h2>
    
    <el-form :model="form" label-width="120px">
      <el-form-item label="选择多个用户">
        <UserSearchSelectMultiple
          v-model="form.userIds"
          placeholder="请输入用户名搜索"
          custom-class="!w-400px"
          @change="handleUsersChange"
        />
      </el-form-item>
      
      <el-form-item v-if="selectedUsers.length">
        <el-descriptions title="已选择用户列表" :column="1" border>
          <el-descriptions-item label="选择的用户数量">{{ selectedUsers.length }}</el-descriptions-item>
        </el-descriptions>
        
        <el-table :data="selectedUsers" style="width: 100%; margin-top: 15px" border>
          <el-table-column label="用户ID" prop="id" width="80" />
          <el-table-column label="用户名" prop="username" width="120" />
          <el-table-column label="昵称" prop="nickname" width="120" />
          <el-table-column label="手机号" prop="mobile" width="120" />
          <el-table-column label="邮箱" prop="email" />
          <el-table-column label="操作" width="80">
            <template #default="scope">
              <el-button type="danger" link @click="removeUser(scope.row.id)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import UserSearchSelectMultiple from './multiple.vue'
import type { UserVO } from '@/api/system/user'

defineOptions({ name: 'UserSearchSelectMultipleExample' })

const form = reactive({
  userIds: [] as number[]
})

const selectedUsers = ref<UserVO[]>([])

const handleUsersChange = (users: UserVO[]) => {
  selectedUsers.value = users
  console.log('Selected users:', users)
}

const removeUser = (userId: number) => {
  form.userIds = form.userIds.filter(id => id !== userId)
  selectedUsers.value = selectedUsers.value.filter(user => user.id !== userId)
}
</script> 