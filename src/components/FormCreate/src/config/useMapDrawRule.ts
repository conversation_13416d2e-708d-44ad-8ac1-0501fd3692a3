import { generateUUID } from '@/utils'
import { localeProps, makeRequiredRule } from '@/components/FormCreate/src/utils'

/**
 * 地图绘图组件规则
 */
export const useMapDrawRule = () => {
  const label = '地图绘图'
  const name = 'MapDraw'
  return {
    // 组件基本信息
    icon: 'icon-direction',
    label,
    name,
    
    // 组件规则定义
    rule() {
      return {
        type: name,
        field: generateUUID(),
        title: label,
        info: '',
        required: false
      }
    },
    
    // 组件属性配置
    props(_, { t }) {
      return localeProps(t, name + '.props', [
        makeRequiredRule(),
        {
          type: 'switch',
          field: 'allowMultiple',
          title: '允许绘制多个图形',
          value: false
        },
        {
          type: 'checkbox',
          field: 'allowedModes',
          title: '允许的绘制模式',
          value: ['Point', 'LineString', 'Polygon'],
          options: [
            { label: '点', value: 'Point' },
            { label: '线', value: 'LineString' },
            { label: '面', value: 'Polygon' }
          ]
        },
        {
          type: 'switch',
          field: 'showToolbar',
          title: '显示工具栏',
          value: true
        },
        {
          type: 'switch',
          field: 'readonly',
          title: '只读模式',
          value: false
        },
        {
          type: 'input',
          field: 'height',
          title: '地图高度',
          value: '400px'
        },
        {
          type: 'input',
          field: 'width',
          title: '地图宽度',
          value: '100%'
        },
        {
          type: 'inputNumber',
          field: 'zoom',
          title: '缩放级别',
          value: 10,
          props: { min: 1, max: 18, step: 1 }
        },
        {
          type: 'input',
          field: 'centerLongitude',
          title: '地图中心经度',
          value: '116.404',
          info: '地图初始中心点经度坐标'
        },
        {
          type: 'input',
          field: 'centerLatitude', 
          title: '地图中心纬度',
          value: '39.915',
          info: '地图初始中心点纬度坐标'
        },
        {
          type: 'input',
          field: 'tiandituToken',
          title: '天地图Token',
          value: '4989e906aa138e5bb1b49a3eb83a6128',
          info: '天地图API的访问令牌'
        },
        {
          type: 'select',
          field: 'projection',
          title: '坐标系统',
          value: 'EPSG:4326',
          options: [
            { label: 'WGS84 地理坐标系 (EPSG:4326)', value: 'EPSG:4326' },
            { label: 'Web墨卡托投影 (EPSG:3857)', value: 'EPSG:3857' }
          ]
        },
        {
          type: 'textarea',
          field: 'placeholder',
          title: '占位提示文字',
          value: '点击绘制地理图形',
          props: { rows: 2 }
        }
      ])
    }
  }
}
