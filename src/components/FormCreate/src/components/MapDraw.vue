<template>
  <div class="form-create-map-draw">
    <!-- 数据显示区域 -->
    <div 
      class="form-create-map-draw__display"
      :class="{
        'form-create-map-draw__display--has-data': hasData,
        'form-create-map-draw__display--readonly': readonly
      }"
      @click="openMapDrawDialog"
    >
      <div v-if="!hasData" class="form-create-map-draw__placeholder">
        <el-icon class="form-create-map-draw__placeholder-icon">
          <Location />
        </el-icon>
        <span class="form-create-map-draw__placeholder-text">
          {{ placeholder || '点击绘制地理图形' }}
        </span>
      </div>
      
      <div v-else class="form-create-map-draw__content">
        <!-- 数据预览 -->
        <div class="form-create-map-draw__preview">
          <el-icon class="form-create-map-draw__preview-icon">
            <MapLocation />
          </el-icon>
          <div class="form-create-map-draw__preview-text">
            <div class="form-create-map-draw__preview-summary">{{ getDataSummary() }}</div>
            <div v-if="showPreviewData" class="form-create-map-draw__preview-data">
              {{ formatPreviewData(modelValue) }}
            </div>
          </div>
          
          <!-- 数据状态标识 -->
          <div class="form-create-map-draw__status">
            <el-icon class="form-create-map-draw__status-icon">
              <CircleCheckFilled />
            </el-icon>
            <span class="form-create-map-draw__status-text">已绘制</span>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="form-create-map-draw__actions">
          <el-button
            v-if="allowEdit"
            type="primary"
            size="small"
            @click.stop="openMapDrawDialog"
            title="编辑地理图形"
          >
            <el-icon><Edit /></el-icon>
          </el-button>
          
          <el-button
            v-if="allowClear"
            type="danger"
            size="small"
            @click.stop="clearData"
            title="清空数据"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
          
          <el-button
            v-if="allowPreview"
            type="info"
            size="small"
            @click.stop="previewData"
            title="预览地理图形"
          >
            <el-icon><View /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 地图绘图弹窗 -->
    <MapDrawDialog
      v-model:visible="dialogVisible"
      v-model:model-value="currentValue"
      :title="dialogTitle"
      :width="dialogWidth"
      :height="dialogHeight"
      :map-height="mapHeight"
      :map-width="mapWidth"
      :readonly="readonly"
      :reference-data="referenceData"
      :reference-styles="referenceStyles"
      :show-toolbar="showToolbar"
      :allowed-modes="allowedModes"
      :allow-multiple="allowMultiple"
      :center="mapCenter"
      :zoom="zoom"
      :projection="projection"
      :tianditu-token="tiandituToken"
      :show-data-preview="showDataPreview"
      :show-clear-button="showClearButton"
      :show-import-button="showImportButton"
      :show-export-button="showExportButton"
      :show-fullscreen-button="showFullscreenButton"
      :show-help-button="showHelpButton"
      :confirm-button-text="confirmButtonText"
      :cancel-button-text="cancelButtonText"
      @confirm="handleConfirm"
      @cancel="handleCancel"
      @draw-start="handleDrawStart"
      @draw-end="handleDrawEnd"
      @feature-select="handleFeatureSelect"
      @feature-modify="handleFeatureModify"
      @feature-delete="handleFeatureDelete"
      @clear="handleClear"
    />

    <!-- 预览弹窗 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="地理图形预览"
      width="70%"
      :append-to-body="true"
      :destroy-on-close="true"
    >
      <MapDrawCore
        v-if="previewDialogVisible"
        :model-value="modelValue"
        :reference-data="referenceData"
        :reference-styles="referenceStyles"
        :readonly="true"
        :show-toolbar="false"
        :allow-multiple="allowMultiple"
        :center="mapCenter"
        :zoom="zoom"
        :projection="projection"
        :tianditu-token="tiandituToken"
        height="400px"
        width="100%"
      />
      
      <template #footer>
        <el-button @click="previewDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElButton, ElIcon, ElDialog, ElMessage, ElMessageBox } from 'element-plus'
import { Location, MapLocation, Edit, Delete, View, CircleCheckFilled } from '@element-plus/icons-vue'
import { MapDrawDialog, MapDrawCore } from '@/components/MapDraw'
import { DrawMode } from '@/components/MapDraw/src/types'
import { WKTUtils } from '@/components/MapDraw/src/utils'

// 定义组件名称
defineOptions({
  name: 'MapDraw'
})

// 组件属性
interface FormCreateMapDrawProps {
  /** 绑定值 (WKT 格式) */
  modelValue?: string
  /** 是否只读 */
  readonly?: boolean
  /** 是否允许编辑 */
  allowEdit?: boolean
  /** 是否允许清空 */
  allowClear?: boolean
  /** 是否允许预览 */
  allowPreview?: boolean
  /** 是否显示预览数据 */
  showPreviewData?: boolean
  /** 占位提示文字 */
  placeholder?: string
  /** 参考数据 */
  referenceData?: string[]
  /** 参考样式 */
  referenceStyles?: any[]
  /** 是否显示工具栏 */
  showToolbar?: boolean
  /** 允许的绘制模式 */
  allowedModes?: DrawMode[]
  /** 是否允许绘制多个图形 */
  allowMultiple?: boolean
  /** 地图中心点经度 */
  centerLongitude?: string
  /** 地图中心点纬度 */
  centerLatitude?: string
  /** 地图缩放级别 */
  zoom?: number
  /** 坐标系 */
  projection?: string
  /** 天地图token */
  tiandituToken?: string
  /** 弹窗标题 */
  dialogTitle?: string
  /** 弹窗宽度 */
  dialogWidth?: string | number
  /** 弹窗高度 */
  dialogHeight?: string | number
  /** 地图容器高度 */
  mapHeight?: string | number
  /** 地图容器宽度 */
  mapWidth?: string | number
  /** 确认按钮文字 */
  confirmButtonText?: string
  /** 取消按钮文字 */
  cancelButtonText?: string
  /** 弹窗选项 */
  showDataPreview?: boolean
  showClearButton?: boolean
  showImportButton?: boolean
  showExportButton?: boolean
  showFullscreenButton?: boolean
  showHelpButton?: boolean
}

const props = withDefaults(defineProps<FormCreateMapDrawProps>(), {
  modelValue: '',
  readonly: false,
  allowEdit: true,
  allowClear: true,
  allowPreview: true,
  showPreviewData: false,
  placeholder: '点击绘制地理图形',
  referenceData: () => [],
  referenceStyles: () => [],
  showToolbar: true,
  allowedModes: () => [DrawMode.POINT, DrawMode.LINE, DrawMode.POLYGON],
  allowMultiple: false,
  centerLongitude: '116.404',
  centerLatitude: '39.915',
  zoom: 10,
  projection: 'EPSG:4326',
  tiandituToken: '4989e906aa138e5bb1b49a3eb83a6128',
  dialogTitle: '地图绘图',
  dialogWidth: '85%',
  dialogHeight: '75%',
  mapHeight: '500px',
  mapWidth: '100%',
  confirmButtonText: '确定',
  cancelButtonText: '取消',
  showDataPreview: true,
  showClearButton: true,
  showImportButton: true,
  showExportButton: true,
  showFullscreenButton: true,
  showHelpButton: true
})

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string]
  'draw-start': [event: { mode: DrawMode; feature: any }]
  'draw-end': [event: { mode: DrawMode; feature: any; wkt: string }]
  'feature-select': [event: { feature: any | null; wkt?: string }]
  'feature-modify': [event: { feature: any; wkt: string }]
  'feature-delete': [event: { feature: any; wkt: string }]
  'clear': []
  'preview': [wkt: string]
}>()

// 响应式数据
const dialogVisible = ref(false)
const previewDialogVisible = ref(false)
const currentValue = ref(props.modelValue)

// 计算属性
/**
 * 是否有数据
 */
const hasData = computed(() => {
  return props.modelValue && props.modelValue.length > 0
})

/**
 * 地图中心点
 */
const mapCenter = computed<[number, number]>(() => {
  const lng = parseFloat(props.centerLongitude || '116.404')
  const lat = parseFloat(props.centerLatitude || '39.915')
  return [lng, lat]
})

/**
 * 获取数据摘要信息
 */
const getDataSummary = () => {
  if (!props.modelValue) return '无数据'
  
  try {
    // 判断是否为几何集合
    if (props.modelValue.includes('GEOMETRYCOLLECTION')) {
      const wktArray = WKTUtils.splitCollectionWKT(props.modelValue)
      const suffix = props.allowMultiple ? ' (多图形模式)' : ' (单图形模式)'
      return `几何集合 (${wktArray.length} 个图形)${suffix}`
    }
    
    // 解析几何类型
    const wkt = props.modelValue.trim().toUpperCase()
    let geometryType = ''
    
    if (wkt.startsWith('POINT')) {
      geometryType = wkt.startsWith('MULTIPOINT') ? '多点' : '点'
    } else if (wkt.startsWith('LINESTRING')) {
      geometryType = wkt.startsWith('MULTILINESTRING') ? '多线' : '线'
    } else if (wkt.startsWith('POLYGON')) {
      geometryType = wkt.startsWith('MULTIPOLYGON') ? '多面' : '面'
    } else {
      geometryType = '未知类型'
    }
    
    const suffix = props.allowMultiple ? ' (多图形模式)' : ' (单图形模式)'
    return geometryType + suffix
  } catch {
    return '数据格式错误'
  }
}

/**
 * 格式化预览数据
 */
const formatPreviewData = (wkt: string) => {
  if (!wkt) return ''
  
  // 限制显示长度
  const maxLength = 100
  if (wkt.length > maxLength) {
    return wkt.substring(0, maxLength) + '...'
  }
  return wkt
}

// 方法
/**
 * 打开地图绘图弹窗
 */
const openMapDrawDialog = () => {
  if (props.readonly) return
  
  currentValue.value = props.modelValue
  dialogVisible.value = true
}

/**
 * 处理确认
 */
const handleConfirm = (wkt: string) => {
  emit('update:modelValue', wkt)
  emit('change', wkt)
}

/**
 * 处理取消
 */
const handleCancel = () => {
  // 恢复原始值
  currentValue.value = props.modelValue
}

/**
 * 清空数据
 */
const clearData = async () => {
  try {
    await ElMessageBox.confirm('确定要清空地理图形数据吗？', '确认清空', {
      type: 'warning'
    })
    
    emit('update:modelValue', '')
    emit('change', '')
    emit('clear')
    ElMessage.success('数据已清空')
  } catch {
    // 用户取消
  }
}

/**
 * 预览数据
 */
const previewData = () => {
  if (!props.modelValue) {
    ElMessage.warning('没有可预览的数据')
    return
  }
  
  emit('preview', props.modelValue)
  previewDialogVisible.value = true
}

// 地图事件处理
const handleDrawStart = (event: { mode: DrawMode; feature: any }) => {
  emit('draw-start', event)
}

const handleDrawEnd = (event: { mode: DrawMode; feature: any; wkt: string }) => {
  emit('draw-end', event)
}

const handleFeatureSelect = (event: { feature: any | null; wkt?: string }) => {
  emit('feature-select', event)
}

const handleFeatureModify = (event: { feature: any; wkt: string }) => {
  emit('feature-modify', event)
}

const handleFeatureDelete = (event: { feature: any; wkt: string }) => {
  emit('feature-delete', event)
}

const handleClear = () => {
  emit('clear')
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  currentValue.value = newValue
})

// 暴露方法
defineExpose({
  /** 打开地图绘图弹窗 */
  openDialog: openMapDrawDialog,
  /** 清空数据 */
  clearData,
  /** 预览数据 */
  previewData,
  /** 获取当前值 */
  getValue: () => props.modelValue,
  /** 设置值 */
  setValue: (wkt: string) => {
    emit('update:modelValue', wkt)
    emit('change', wkt)
  }
})
</script>

<style lang="scss" scoped>
.form-create-map-draw {
  &__display {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    min-height: 60px;
    cursor: pointer;
    transition: all 0.2s;
    background-color: #ffffff;
    
    &:hover {
      border-color: #409eff;
    }
    
    &:focus-within {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
    
    // 有数据状态
    &--has-data {
      border-color: #67c23a;
      background-color: #f0f9ff;
      
      &:hover {
        border-color: #529b2e;
        background-color: #e1f3d8;
      }
    }
    
    // 只读状态
    &--readonly {
      cursor: default;
      background-color: #f5f7fa;
      
      &:hover {
        border-color: #dcdfe6;
      }
      
      &.form-create-map-draw__display--has-data {
        background-color: #f0f9ff;
        border-color: #67c23a;
        
        &:hover {
          border-color: #67c23a;
          background-color: #f0f9ff;
        }
      }
    }
  }

  &__placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 60px;
    color: #c0c4cc;
    font-size: 14px;
  }

  &__placeholder-icon {
    font-size: 24px;
    margin-bottom: 4px;
  }

  &__placeholder-text {
    font-size: 12px;
  }

  &__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    min-height: 60px;
  }

  &__preview {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
  }

  &__preview-icon {
    font-size: 20px;
    color: #67c23a;
    margin-right: 8px;
    flex-shrink: 0;
  }

  &__preview-text {
    flex: 1;
    min-width: 0;
  }

  &__preview-summary {
    font-size: 14px;
    color: #303133;
    font-weight: 500;
    line-height: 1.4;
  }

  &__preview-data {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
    line-height: 1.3;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 数据状态标识
  &__status {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: 8px;
    padding: 4px 8px;
    background-color: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 12px;
    flex-shrink: 0;
  }

  &__status-icon {
    font-size: 14px;
    color: #67c23a;
  }

  &__status-text {
    font-size: 11px;
    color: #529b2e;
    font-weight: 500;
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: 12px;
    flex-shrink: 0;
  }
}
</style> 
