<template>
  <div class="map-draw-core" :style="{ width, height }">
    <!-- 地图容器 -->
    <div 
      ref="mapContainer" 
      class="map-draw-core__map"
      style="width: 100%; height: 100%"
    ></div>

    <!-- 工具栏 -->
    <div v-if="showToolbar && !readonly" class="map-draw-core__toolbar">
      <div class="map-draw-core__toolbar-header">
        <span class="map-draw-core__toolbar-title">绘图工具</span>
      </div>
      <div class="map-draw-core__toolbar-content">
        <!-- 绘制工具 -->
        <div class="map-draw-core__tool-group" title="绘制工具">
          <button
            v-for="tool in drawTools"
            :key="tool.id"
            :class="[
              'map-draw-core__tool-button',
              { 'map-draw-core__tool-button--active': tool.active }
            ]"
            :disabled="tool.disabled"
            :title="tool.label"
            @click="handleToolClick(tool)"
          >
            <el-icon><component :is="iconComponents[tool.icon]" /></el-icon>
            <span class="map-draw-core__tool-label">{{ tool.label }}</span>
          </button>
        </div>

        <!-- 编辑工具 -->
        <div class="map-draw-core__tool-group" title="编辑工具">
          <button
            :class="[
              'map-draw-core__tool-button',
              { 'map-draw-core__tool-button--active': currentDrawMode === null }
            ]"
            title="选择编辑"
            @click="activateSelectMode"
          >
            <el-icon><component :is="iconComponents['el-icon-mouse']" /></el-icon>
            <span class="map-draw-core__tool-label">选择</span>
          </button>
        </div>

        <!-- 操作工具 -->
        <div class="map-draw-core__tool-group" title="操作工具">
          <button
            class="map-draw-core__tool-button"
            :disabled="!canUndo"
            title="撤销"
            @click="undo"
          >
            <el-icon><component :is="iconComponents['el-icon-refresh-left']" /></el-icon>
            <span class="map-draw-core__tool-label">撤销</span>
          </button>
          
          <button
            class="map-draw-core__tool-button"
            :disabled="!canRedo"
            title="重做"
            @click="redo"
          >
            <el-icon><component :is="iconComponents['el-icon-refresh-right']" /></el-icon>
            <span class="map-draw-core__tool-label">重做</span>
          </button>
          
          <button
            class="map-draw-core__tool-button map-draw-core__tool-button--danger"
            :disabled="drawnFeatures.length === 0"
            title="清空"
            @click="clearAllFeatures"
          >
            <el-icon><component :is="iconComponents['el-icon-delete']" /></el-icon>
            <span class="map-draw-core__tool-label">清空</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="map-draw-core__status">
      <div class="map-draw-core__status-item">
        <span class="map-draw-core__status-label">模式:</span>
        <span class="map-draw-core__status-value" :class="{ 'map-draw-core__status-value--drawing': currentDrawMode }">
          {{ getStatusText() }}
        </span>
      </div>
      <div class="map-draw-core__status-item">
        <span class="map-draw-core__status-label">图形数量:</span>
        <span class="map-draw-core__status-value">{{ drawnFeatures.length }}</span>
      </div>
      <div v-if="!allowMultiple && drawnFeatures.length > 0" class="map-draw-core__status-item">
        <span class="map-draw-core__status-label map-draw-core__status-label--warning">
          只允许绘制一个图形
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElIcon, ElMessage } from 'element-plus'
import { 
  Location, 
  Minus, 
  Crop, 
  CirclePlus, 
  Mouse, 
  RefreshLeft, 
  RefreshRight, 
  Delete
} from '@element-plus/icons-vue'
import { Map as OlMap, View } from 'ol'
import { defaults as defaultControls, MousePosition, ScaleLine } from 'ol/control'
import { Tile as TileLayer, Vector as VectorLayer } from 'ol/layer'
import { XYZ, Vector as VectorSource } from 'ol/source'
import { createStringXY } from 'ol/coordinate'
import { WKT } from 'ol/format'
import { Geometry, Point, LineString, Polygon, Circle } from 'ol/geom'
import { Feature } from 'ol'
import { Draw, Modify, Select } from 'ol/interaction'
import { Style, Fill, Stroke, Circle as CircleStyle, Icon } from 'ol/style'
import { never, click, pointerMove } from 'ol/events/condition'
import { DrawMode, type MapDrawProps, type ToolButton } from './types'

// 组件属性
const props = withDefaults(defineProps<MapDrawProps>(), {
  modelValue: '',
  referenceData: () => [],
  referenceStyles: () => [],
  showToolbar: true,
  allowedModes: () => [DrawMode.POINT, DrawMode.LINE, DrawMode.POLYGON],
  center: () => [116.404, 39.915],
  zoom: 10,
  projection: 'EPSG:4326',
  readonly: false,
  height: '400px',
  width: '100%',
  allowMultiple: false,
  tiandituToken: '4989e906aa138e5bb1b49a3eb83a6128'
})

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [wkt: string]
  'draw-start': [event: { mode: DrawMode; feature: any }]
  'draw-end': [event: { mode: DrawMode; feature: any; wkt: string }]
  'feature-select': [event: { feature: any | null; wkt?: string }]
  'feature-modify': [event: { feature: any; wkt: string }]
  'feature-delete': [event: { feature: any; wkt: string }]
  'clear': []
}>()

// 响应式数据
const mapContainer = ref<HTMLElement>()
const mapInstance = ref<OlMap>()
const drawSource = ref<VectorSource>()
const referenceSource = ref<VectorSource>()
const drawLayer = ref<VectorLayer<VectorSource>>()
const referenceLayer = ref<VectorLayer<VectorSource>>()
const currentDrawInteraction = ref<Draw>()
const selectInteraction = ref<Select>()
const modifyInteraction = ref<Modify>()

const currentDrawMode = ref<DrawMode | null>(null)
const drawnFeatures = ref<any[]>([])
const selectedFeature = ref<any>(null)
const history = ref<string[]>([])
const historyIndex = ref(-1)

// 图标组件映射
const iconComponents = {
  'el-icon-location': Location,
  'el-icon-position': Location,
  'el-icon-minus': Minus,
  'el-icon-crop': Crop,
  'el-icon-circle-plus': CirclePlus,
  'el-icon-mouse': Mouse,
  'el-icon-refresh-left': RefreshLeft,
  'el-icon-refresh-right': RefreshRight,
  'el-icon-delete': Delete
}

// WKT格式化器
const wktFormat = new WKT()

// 坐标格式化器
const coordinateFormat = createStringXY(4)

// 创建定位图标SVG
const createLocationIcon = (color: string, strokeColor: string = '#ffffff', size: number = 24) => {
  const svg = `
    <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 24 24" fill="${color}" stroke="${strokeColor}" stroke-width="2">
      <path d="M12 2C8.686 2 6 4.686 6 8c0 5.25 6 12 6 12s6-6.75 6-12c0-3.314-2.686-6-6-6z"/>
      <circle cx="12" cy="8" r="2" fill="${strokeColor}"/>
    </svg>
  `
  return 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svg)
}

// 计算属性
const drawTools = computed<ToolButton[]>(() => {
  const tools: ToolButton[] = []
  
  if (props.allowedModes.includes(DrawMode.POINT)) {
    tools.push({
      id: 'point',
      label: '点',
      icon: 'el-icon-position',
      mode: DrawMode.POINT,
      active: currentDrawMode.value === DrawMode.POINT,
      disabled: !props.allowMultiple && drawnFeatures.value.length > 0
    })
  }
  
  if (props.allowedModes.includes(DrawMode.LINE)) {
    tools.push({
      id: 'line',
      label: '线',
      icon: 'el-icon-minus',
      mode: DrawMode.LINE,
      active: currentDrawMode.value === DrawMode.LINE,
      disabled: !props.allowMultiple && drawnFeatures.value.length > 0
    })
  }
  
  if (props.allowedModes.includes(DrawMode.POLYGON)) {
    tools.push({
      id: 'polygon',
      label: '面',
      icon: 'el-icon-crop',
      mode: DrawMode.POLYGON,
      active: currentDrawMode.value === DrawMode.POLYGON,
      disabled: !props.allowMultiple && drawnFeatures.value.length > 0
    })
  }
  
  return tools
})

const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

// 样式定义
const drawStyle = new Style({
  fill: new Fill({
    color: 'rgba(64, 158, 255, 0.1)'
  }),
  stroke: new Stroke({
    color: '#409eff',
    width: 2
  }),
  image: new Icon({
    src: createLocationIcon('#409eff'),
    anchor: [0.5, 1],
    anchorXUnits: 'fraction',
    anchorYUnits: 'fraction',
    scale: 1
  })
})

const referenceStyle = new Style({
  fill: new Fill({
    color: 'rgba(103, 194, 58, 0.1)'
  }),
  stroke: new Stroke({
    color: '#67c23a',
    width: 1
  }),
  image: new Icon({
    src: createLocationIcon('#67c23a'),
    anchor: [0.5, 1],
    anchorXUnits: 'fraction',
    anchorYUnits: 'fraction',
    scale: 0.8
  })
})

const selectStyle = new Style({
  fill: new Fill({
    color: 'rgba(64, 158, 255, 0.2)'
  }),
  stroke: new Stroke({
    color: '#409eff',
    width: 3
  }),
  image: new Icon({
    src: createLocationIcon('#ff4757'),
    anchor: [0.5, 1],
    anchorXUnits: 'fraction',
    anchorYUnits: 'fraction',
    scale: 1.2
  })
})

// 根据几何类型返回相应样式的函数
const getStyleForFeature = (feature: any, styleType: 'draw' | 'reference' | 'select' = 'draw') => {
  const geometry = feature.getGeometry()
  const geometryType = geometry ? geometry.getType() : null
  
  if (geometryType === 'Point') {
    // 点使用定位图标
    if (styleType === 'draw') {
      return drawStyle
    } else if (styleType === 'reference') {
      return referenceStyle
    } else if (styleType === 'select') {
      return selectStyle
    }
  } else {
    // 线和面使用原来的样式（不使用图标）
    if (styleType === 'draw') {
      return new Style({
        fill: new Fill({
          color: 'rgba(64, 158, 255, 0.1)'
        }),
        stroke: new Stroke({
          color: '#409eff',
          width: 2
        })
      })
    } else if (styleType === 'reference') {
      return new Style({
        fill: new Fill({
          color: 'rgba(103, 194, 58, 0.1)'
        }),
        stroke: new Stroke({
          color: '#67c23a',
          width: 1
        })
      })
    } else if (styleType === 'select') {
      return new Style({
        fill: new Fill({
          color: 'rgba(64, 158, 255, 0.2)'
        }),
        stroke: new Stroke({
          color: '#409eff',
          width: 3
        })
      })
    }
  }
  
  return drawStyle // 默认样式
}

// 方法
const initializeMap = () => {
  if (!mapContainer.value) return

  // 创建数据源
  drawSource.value = new VectorSource()
  referenceSource.value = new VectorSource()

  // 创建图层
  drawLayer.value = new VectorLayer({
    source: drawSource.value,
    style: (feature) => getStyleForFeature(feature, 'draw')
  })

  referenceLayer.value = new VectorLayer({
    source: referenceSource.value,
    style: (feature) => getStyleForFeature(feature, 'reference')
  })

  // 创建天地图图层
  const tiandituImageLayer = new TileLayer({
    source: new XYZ({
      url: `http://t{0-7}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${props.tiandituToken}`
    })
  })

  const tiandituLabelLayer = new TileLayer({
    source: new XYZ({
      url: `http://t{0-7}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${props.tiandituToken}`
    })
  })

  // 创建地图实例
  mapInstance.value = new OlMap({
    target: mapContainer.value,
    layers: [tiandituImageLayer, tiandituLabelLayer, referenceLayer.value, drawLayer.value],
    view: new View({
      center: props.center,
      zoom: props.zoom,
      projection: props.projection
    }),
    controls: defaultControls().extend([
      new MousePosition({
        coordinateFormat: coordinateFormat,
        projection: props.projection,
        className: 'map-draw-mouse-position'
      }),
      new ScaleLine()
    ])
  })

  // 创建选择交互
  selectInteraction.value = new Select({
    layers: [drawLayer.value],
    style: (feature) => getStyleForFeature(feature, 'select'),
    condition: click
  })

  // 创建修改交互
  modifyInteraction.value = new Modify({
    features: selectInteraction.value.getFeatures()
  })

  // 添加交互到地图
  mapInstance.value.addInteraction(selectInteraction.value)
  mapInstance.value.addInteraction(modifyInteraction.value)

  // 绑定事件
  selectInteraction.value.on('select', handleFeatureSelect)
  modifyInteraction.value.on('modifyend', handleModifyEnd)

  // 初始化数据
  nextTick(() => {
    if (props.modelValue) {
      loadWKTData(props.modelValue)
    }
    if (props.referenceData.length > 0) {
      loadReferenceData()
    }
  })
}

const loadReferenceData = () => {
  if (!referenceSource.value) return

  referenceSource.value.clear()
  props.referenceData.forEach(wkt => {
    try {
      const geometry = wktFormat.readGeometry(wkt, {
        dataProjection: props.projection,
        featureProjection: props.projection
      })
      if (geometry) {
        const feature = new Feature(geometry)
        feature.set('type', 'reference')
        referenceSource.value!.addFeature(feature)
      }
    } catch (error) {
      console.warn('解析参考数据失败:', error)
    }
  })
}

const loadWKTData = (wkt: string) => {
  if (!drawSource.value) return

  drawnFeatures.value = []
  drawSource.value.clear()

  if (!wkt) return

  try {
    // 检查是否为几何集合
    if (wkt.includes('GEOMETRYCOLLECTION')) {
      // 解析几何集合
      const geometry = wktFormat.readGeometry(wkt, {
        dataProjection: props.projection,
        featureProjection: props.projection
      })
      
      if (geometry && geometry.getType() === 'GeometryCollection') {
        const geometries = (geometry as any).getGeometries()
        geometries.forEach((geom: Geometry, index: number) => {
          const feature = new Feature(geom)
          feature.set('type', 'drawn')
          feature.set('index', index)
          drawnFeatures.value.push(feature)
          drawSource.value!.addFeature(feature)
        })
      }
    } else {
      // 单个几何图形
      const geometry = wktFormat.readGeometry(wkt, {
        dataProjection: props.projection,
        featureProjection: props.projection
      })
      if (geometry) {
        const feature = new Feature(geometry)
        feature.set('type', 'drawn')
        feature.set('index', 0)
        drawnFeatures.value.push(feature)
        drawSource.value.addFeature(feature)
      }
    }
  } catch (error) {
    console.error('加载WKT数据失败:', error)
    ElMessage.error('WKT数据格式错误')
  }
}

const generateWKT = (): string => {
  if (drawnFeatures.value.length === 0) return ''
  
  if (drawnFeatures.value.length === 1) {
    return wktFormat.writeGeometry(drawnFeatures.value[0].getGeometry()!, {
      dataProjection: props.projection,
      featureProjection: props.projection
    })
  } else {
    // 多个几何图形组成几何集合
    const geometries = drawnFeatures.value.map(f => f.getGeometry()!)
    const wktStrings = geometries.map(geom => 
      wktFormat.writeGeometry(geom, {
        dataProjection: props.projection,
        featureProjection: props.projection
      })
    )
    return `GEOMETRYCOLLECTION(${wktStrings.join(', ')})`
  }
}

const saveToHistory = () => {
  const wkt = generateWKT()
  
  // 删除当前位置之后的历史记录
  if (historyIndex.value < history.value.length - 1) {
    history.value = history.value.slice(0, historyIndex.value + 1)
  }
  
  history.value.push(wkt)
  historyIndex.value = history.value.length - 1
  
  // 限制历史记录数量
  if (history.value.length > 20) {
    history.value.shift()
    historyIndex.value--
  }
}

const handleToolClick = (tool: ToolButton) => {
  if (tool.disabled) return
  
  if (tool.mode) {
    activateDrawMode(tool.mode)
  }
}

const activateDrawMode = (mode: DrawMode) => {
  if (props.readonly) return
  
  if (!props.allowMultiple && drawnFeatures.value.length > 0) {
    ElMessage.warning('当前模式下只允许绘制一个图形，请先清空现有图形')
    return
  }
  
  // 移除现有的绘制交互
  if (currentDrawInteraction.value) {
    mapInstance.value!.removeInteraction(currentDrawInteraction.value)
  }
  
  currentDrawMode.value = mode
  selectedFeature.value = null
  
  // 创建新的绘制交互
  currentDrawInteraction.value = new Draw({
    source: drawSource.value!,
    type: mode
  })
  
  // 绑定事件
  currentDrawInteraction.value.on('drawstart', handleDrawStart)
  currentDrawInteraction.value.on('drawend', handleDrawEnd)
  
  // 添加到地图
  mapInstance.value!.addInteraction(currentDrawInteraction.value)
}

const activateSelectMode = () => {
  // 移除绘制交互
  if (currentDrawInteraction.value) {
    mapInstance.value!.removeInteraction(currentDrawInteraction.value)
    currentDrawInteraction.value = undefined
  }
  
  currentDrawMode.value = null
}

const handleDrawStart = (event: any) => {
  emit('draw-start', {
    mode: currentDrawMode.value!,
    feature: event.feature
  })
}

const handleDrawEnd = (event: any) => {
  const feature = event.feature as Feature<Geometry>
  
  // 检查是否允许多个图形
  if (!props.allowMultiple && drawnFeatures.value.length > 0) {
    // 替换现有图形
    drawnFeatures.value.forEach(f => drawSource.value!.removeFeature(f))
    drawnFeatures.value = [feature]
  } else {
    // 添加新图形
    drawnFeatures.value.push(feature)
  }
  
  feature.set('type', 'drawn')
  feature.set('index', drawnFeatures.value.length - 1)
  
  const wkt = generateWKT()
  emit('update:modelValue', wkt)
  emit('draw-end', {
    mode: currentDrawMode.value!,
    feature,
    wkt
  })
  
  saveToHistory()
  
  // 绘制完成后切换到选择模式
  activateSelectMode()
}

const handleFeatureSelect = (event: any) => {
  const features = event.selected
  selectedFeature.value = features.length > 0 ? features[0] : null
  
  emit('feature-select', {
    feature: selectedFeature.value,
    wkt: selectedFeature.value ? generateWKT() : undefined
  })
}

const handleModifyEnd = (event: any) => {
  const features = event.features.getArray()
  if (features.length > 0) {
    const feature = features[0]
    const wkt = generateWKT()
    
    emit('update:modelValue', wkt)
    emit('feature-modify', { feature, wkt })
    
    saveToHistory()
  }
}

const clearAllFeatures = () => {
  drawnFeatures.value = []
  selectedFeature.value = null
  currentDrawMode.value = null
  
  if (drawSource.value) {
    drawSource.value.clear()
  }
  
  // 移除绘制交互
  if (currentDrawInteraction.value) {
    mapInstance.value!.removeInteraction(currentDrawInteraction.value)
    currentDrawInteraction.value = undefined
  }
  
  const wkt = ''
  emit('update:modelValue', wkt)
  emit('clear')
  
  saveToHistory()
}

const undo = () => {
  if (!canUndo.value) return
  
  historyIndex.value--
  const wkt = history.value[historyIndex.value]
  loadWKTData(wkt)
  emit('update:modelValue', wkt)
}

const redo = () => {
  if (!canRedo.value) return
  
  historyIndex.value++
  const wkt = history.value[historyIndex.value]
  loadWKTData(wkt)
  emit('update:modelValue', wkt)
}

const getStatusText = () => {
  if (props.readonly) return '只读模式'
  if (currentDrawMode.value) {
    const modeTexts = {
      [DrawMode.POINT]: '绘制点',
      [DrawMode.LINE]: '绘制线',
      [DrawMode.POLYGON]: '绘制面'
    }
    return modeTexts[currentDrawMode.value] || '绘制中'
  }
  return '选择模式'
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue !== generateWKT()) {
    loadWKTData(newValue)
  }
})

watch(() => props.referenceData, () => {
  loadReferenceData()
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initializeMap()
    saveToHistory()
  })
})

onUnmounted(() => {
  if (mapInstance.value) {
    mapInstance.value.dispose()
  }
})

// 暴露方法
defineExpose({
  getMap: () => mapInstance.value,
  getWKTData: generateWKT,
  setWKTData: loadWKTData,
  clearData: clearAllFeatures,
  activateDrawMode,
  activateEditMode: activateSelectMode,
  undo,
  redo
})
</script>

<style lang="scss" scoped>
.map-draw-core {
  position: relative;
  display: flex;
  flex-direction: column;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
  
  &--readonly {
    .map-draw-core__toolbar {
      display: none;
    }
    
    .map-draw-core__status {
      background-color: #f5f7fa;
    }
  }

  &__toolbar {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    max-width: 250px;
    backdrop-filter: blur(4px);

    &-header {
      padding: 8px 12px;
      background: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
      font-size: 12px;
      color: #606266;
      font-weight: 500;
    }

    &-content {
      padding: 8px;
    }
  }

  &__tool-group {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  &__tool-button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 8px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: #ffffff;
    color: #606266;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 0;
    flex: 1;

    &:hover:not(:disabled) {
      border-color: #409eff;
      color: #409eff;
      background: #ecf5ff;
    }

    &--active {
      border-color: #409eff;
      color: #409eff;
      background: #ecf5ff;
    }

    &--danger {
      &:hover:not(:disabled) {
        border-color: #f56c6c;
        color: #f56c6c;
        background: #fef0f0;
      }
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
      background: #f5f7fa;
    }

    .el-icon {
      font-size: 14px;
      flex-shrink: 0;
    }
  }

  &__tool-label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1;
  }

  &__status {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.95);
    border-top: 1px solid #e4e7ed;
    font-size: 12px;
    backdrop-filter: blur(4px);

    &-item {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    &-label {
      color: #909399;
      font-weight: 500;
      
      &--warning {
        color: #e6a23c;
      }
    }

    &-value {
      color: #303133;
      font-weight: 500;

      &--drawing {
        color: #409eff;
      }
    }
  }
}

// 鼠标位置控件样式
:global(.map-draw-mouse-position) {
  position: absolute !important;
  bottom: 40px !important;
  right: 10px !important;
  color: #ffffff;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  z-index: 1000;
  backdrop-filter: blur(4px);
}
</style> 
