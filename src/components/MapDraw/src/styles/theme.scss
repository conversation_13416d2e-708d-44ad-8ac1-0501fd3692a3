/**
 * 地图绘图组件主题样式
 * 支持深色/浅色模式切换和自定义主题
 */

// 主题变量定义
:root {
  // 浅色主题（默认）
  --map-draw-bg-color: #ffffff;
  --map-draw-border-color: #dcdfe6;
  --map-draw-border-hover-color: #409eff;
  --map-draw-text-color: #303133;
  --map-draw-text-secondary-color: #606266;
  --map-draw-text-placeholder-color: #c0c4cc;
  --map-draw-shadow-color: rgba(0, 0, 0, 0.1);
  
  // 工具栏
  --map-draw-toolbar-bg: rgba(255, 255, 255, 0.95);
  --map-draw-toolbar-border: #e4e7ed;
  --map-draw-toolbar-header-bg: #f5f7fa;
  
  // 按钮
  --map-draw-button-primary-bg: #409eff;
  --map-draw-button-primary-hover-bg: #66b1ff;
  --map-draw-button-danger-bg: #f56c6c;
  --map-draw-button-danger-hover-bg: #f78989;
  --map-draw-button-warning-bg: #e6a23c;
  --map-draw-button-warning-hover-bg: #ebb563;
  
  // 状态栏
  --map-draw-status-bg: rgba(255, 255, 255, 0.95);
  --map-draw-status-border: #e4e7ed;
  
  // 右键菜单
  --map-draw-context-menu-bg: #ffffff;
  --map-draw-context-menu-border: #e4e7ed;
  --map-draw-context-menu-hover-bg: #f5f7fa;
  --map-draw-context-menu-hover-color: #409eff;
  
  // 提示信息
  --map-draw-tooltip-bg: rgba(0, 0, 0, 0.8);
  --map-draw-tooltip-color: #ffffff;
  --map-draw-tooltip-success-bg: rgba(103, 194, 58, 0.9);
  --map-draw-tooltip-warning-bg: rgba(230, 162, 60, 0.9);
  --map-draw-tooltip-error-bg: rgba(245, 108, 108, 0.9);
  
  // 表单组件
  --map-draw-form-bg: #ffffff;
  --map-draw-form-border: #dcdfe6;
  --map-draw-form-border-hover: #409eff;
  --map-draw-form-border-focus: #409eff;
  --map-draw-form-focus-shadow: rgba(64, 158, 255, 0.2);
  --map-draw-form-readonly-bg: #f5f7fa;
  --map-draw-form-disabled-bg: #f5f7fa;
  --map-draw-form-disabled-color: #c0c4cc;
  --map-draw-form-error-border: #f56c6c;
  --map-draw-form-error-shadow: rgba(245, 108, 108, 0.2);
}

// 深色主题
[data-theme="dark"] {
  --map-draw-bg-color: #1f1f1f;
  --map-draw-border-color: #414243;
  --map-draw-border-hover-color: #409eff;
  --map-draw-text-color: #e5eaf3;
  --map-draw-text-secondary-color: #a3a6ad;
  --map-draw-text-placeholder-color: #6c6e72;
  --map-draw-shadow-color: rgba(0, 0, 0, 0.3);
  
  // 工具栏
  --map-draw-toolbar-bg: rgba(31, 31, 31, 0.95);
  --map-draw-toolbar-border: #414243;
  --map-draw-toolbar-header-bg: #262727;
  
  // 按钮
  --map-draw-button-primary-bg: #409eff;
  --map-draw-button-primary-hover-bg: #66b1ff;
  --map-draw-button-danger-bg: #f56c6c;
  --map-draw-button-danger-hover-bg: #f78989;
  --map-draw-button-warning-bg: #e6a23c;
  --map-draw-button-warning-hover-bg: #ebb563;
  
  // 状态栏
  --map-draw-status-bg: rgba(31, 31, 31, 0.95);
  --map-draw-status-border: #414243;
  
  // 右键菜单
  --map-draw-context-menu-bg: #1f1f1f;
  --map-draw-context-menu-border: #414243;
  --map-draw-context-menu-hover-bg: #262727;
  --map-draw-context-menu-hover-color: #409eff;
  
  // 提示信息
  --map-draw-tooltip-bg: rgba(31, 31, 31, 0.9);
  --map-draw-tooltip-color: #e5eaf3;
  --map-draw-tooltip-success-bg: rgba(103, 194, 58, 0.9);
  --map-draw-tooltip-warning-bg: rgba(230, 162, 60, 0.9);
  --map-draw-tooltip-error-bg: rgba(245, 108, 108, 0.9);
  
  // 表单组件
  --map-draw-form-bg: #1f1f1f;
  --map-draw-form-border: #414243;
  --map-draw-form-border-hover: #409eff;
  --map-draw-form-border-focus: #409eff;
  --map-draw-form-focus-shadow: rgba(64, 158, 255, 0.2);
  --map-draw-form-readonly-bg: #262727;
  --map-draw-form-disabled-bg: #262727;
  --map-draw-form-disabled-color: #6c6e72;
  --map-draw-form-error-border: #f56c6c;
  --map-draw-form-error-shadow: rgba(245, 108, 108, 0.2);
}

// 主题混入
@mixin map-draw-theme {
  background-color: var(--map-draw-bg-color);
  border-color: var(--map-draw-border-color);
  color: var(--map-draw-text-color);
}

@mixin map-draw-toolbar-theme {
  background: var(--map-draw-toolbar-bg);
  border-color: var(--map-draw-toolbar-border);
  box-shadow: 0 2px 12px var(--map-draw-shadow-color);
}

@mixin map-draw-button-theme($type: 'default') {
  @if $type == 'primary' {
    background-color: var(--map-draw-button-primary-bg);
    &:hover {
      background-color: var(--map-draw-button-primary-hover-bg);
    }
  } @else if $type == 'danger' {
    background-color: var(--map-draw-button-danger-bg);
    &:hover {
      background-color: var(--map-draw-button-danger-hover-bg);
    }
  } @else if $type == 'warning' {
    background-color: var(--map-draw-button-warning-bg);
    &:hover {
      background-color: var(--map-draw-button-warning-hover-bg);
    }
  }
}

@mixin map-draw-form-theme {
  background-color: var(--map-draw-form-bg);
  border-color: var(--map-draw-form-border);
  color: var(--map-draw-text-color);
  
  &:hover {
    border-color: var(--map-draw-form-border-hover);
  }
  
  &:focus-within {
    border-color: var(--map-draw-form-border-focus);
    box-shadow: 0 0 0 2px var(--map-draw-form-focus-shadow);
  }
  
  &.readonly {
    background-color: var(--map-draw-form-readonly-bg);
    &:hover {
      border-color: var(--map-draw-form-border);
    }
  }
  
  &.disabled {
    background-color: var(--map-draw-form-disabled-bg);
    color: var(--map-draw-form-disabled-color);
    cursor: not-allowed;
    &:hover {
      border-color: var(--map-draw-form-border);
    }
  }
  
  &.error {
    border-color: var(--map-draw-form-error-border);
    &:hover {
      border-color: var(--map-draw-form-error-border);
    }
    &:focus-within {
      border-color: var(--map-draw-form-error-border);
      box-shadow: 0 0 0 2px var(--map-draw-form-error-shadow);
    }
  }
}

// 组件样式应用
.map-draw-core {
  @include map-draw-theme;
  
  &__toolbar {
    @include map-draw-toolbar-theme;
    
    &-header {
      background: var(--map-draw-toolbar-header-bg);
      border-bottom-color: var(--map-draw-toolbar-border);
    }
    
    &-title {
      color: var(--map-draw-text-secondary-color);
    }
  }
  
  &__status {
    background: var(--map-draw-status-bg);
    border-top-color: var(--map-draw-status-border);
    
    &-label {
      color: var(--map-draw-text-secondary-color);
    }
    
    &-value {
      color: var(--map-draw-text-color);
      
      &--drawing {
        color: var(--map-draw-button-primary-bg);
      }
    }
  }
  
  &__context-menu {
    background: var(--map-draw-context-menu-bg);
    border-color: var(--map-draw-context-menu-border);
    box-shadow: 0 2px 12px var(--map-draw-shadow-color);
    
    &-item {
      color: var(--map-draw-text-secondary-color);
      
      &:hover:not(&--disabled) {
        background: var(--map-draw-context-menu-hover-bg);
        color: var(--map-draw-context-menu-hover-color);
      }
      
      &--disabled {
        color: var(--map-draw-text-placeholder-color);
      }
    }
  }
  
  &__tooltip {
    background: var(--map-draw-tooltip-bg);
    color: var(--map-draw-tooltip-color);
    
    &--success {
      background: var(--map-draw-tooltip-success-bg);
    }
    
    &--warning {
      background: var(--map-draw-tooltip-warning-bg);
    }
    
    &--error {
      background: var(--map-draw-tooltip-error-bg);
    }
  }
}

.map-draw-dialog {
  .el-dialog {
    background-color: var(--map-draw-bg-color);
    color: var(--map-draw-text-color);
  }
  
  &__header {
    color: var(--map-draw-text-color);
  }
  
  &__title {
    color: var(--map-draw-text-color);
  }
  
  &__content {
    border-color: var(--map-draw-border-color);
  }
  
  &__footer {
    border-top-color: var(--map-draw-border-color);
  }
  
  &__help {
    h4 {
      color: var(--map-draw-text-color);
    }
    
    li {
      color: var(--map-draw-text-secondary-color);
    }
    
    kbd {
      background: var(--map-draw-form-readonly-bg);
      border-color: var(--map-draw-border-color);
      color: var(--map-draw-text-secondary-color);
    }
  }
}

.form-create-map-draw {
  &__display {
    @include map-draw-form-theme;
  }
  
  &__placeholder {
    color: var(--map-draw-text-placeholder-color);
  }
  
  &__preview-icon {
    color: var(--map-draw-button-primary-bg);
  }
  
  &__preview-summary {
    color: var(--map-draw-text-color);
  }
  
  &__preview-data {
    color: var(--map-draw-text-secondary-color);
  }
  
  // 状态修饰符
  &--readonly {
    .form-create-map-draw__display {
      @extend .readonly;
    }
  }
  
  &--disabled {
    .form-create-map-draw__display {
      @extend .disabled;
    }
  }
  
  &--error {
    .form-create-map-draw__display {
      @extend .error;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .map-draw-core {
    &__toolbar {
      min-width: 150px;
      max-width: 200px;
      
      &--bottom-left,
      &--bottom-right {
        bottom: 10px;
      }
    }
    
    &__tool-button {
      padding: 4px 6px;
      
      .map-draw-core__tool-label {
        display: none;
      }
    }
    
    &__status {
      height: 35px;
      font-size: 11px;
      padding: 0 8px;
      gap: 12px;
    }
  }
  
  .map-draw-dialog {
    &__footer {
      flex-direction: column;
      gap: 12px;
      
      .map-draw-dialog__preview {
        order: 2;
      }
      
      .map-draw-dialog__actions {
        order: 1;
        justify-content: center;
      }
    }
  }
  
  .form-create-map-draw {
    &__content {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;
    }
    
    &__actions {
      margin-left: 0;
      justify-content: center;
    }
  }
}

// 打印样式
@media print {
  .map-draw-core {
    &__toolbar,
    &__status {
      display: none !important;
    }
  }
  
  .map-draw-dialog {
    &__header-actions,
    &__footer {
      display: none !important;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  :root {
    --map-draw-border-color: #000000;
    --map-draw-border-hover-color: #0066cc;
    --map-draw-text-color: #000000;
    --map-draw-text-secondary-color: #333333;
    --map-draw-shadow-color: rgba(0, 0, 0, 0.5);
  }
  
  [data-theme="dark"] {
    --map-draw-border-color: #ffffff;
    --map-draw-border-hover-color: #66b1ff;
    --map-draw-text-color: #ffffff;
    --map-draw-text-secondary-color: #cccccc;
    --map-draw-shadow-color: rgba(255, 255, 255, 0.3);
  }
}

// 动画效果
.map-draw-fade-enter-active,
.map-draw-fade-leave-active {
  transition: opacity 0.3s ease;
}

.map-draw-fade-enter-from,
.map-draw-fade-leave-to {
  opacity: 0;
}

.map-draw-slide-enter-active,
.map-draw-slide-leave-active {
  transition: transform 0.3s ease;
}

.map-draw-slide-enter-from {
  transform: translateY(-10px);
}

.map-draw-slide-leave-to {
  transform: translateY(10px);
}

// 工具类
.map-draw-hidden {
  display: none !important;
}

.map-draw-invisible {
  visibility: hidden !important;
}

.map-draw-no-pointer-events {
  pointer-events: none !important;
}

.map-draw-full-width {
  width: 100% !important;
}

.map-draw-full-height {
  height: 100% !important;
}

.map-draw-text-center {
  text-align: center !important;
}

.map-draw-text-left {
  text-align: left !important;
}

.map-draw-text-right {
  text-align: right !important;
} 