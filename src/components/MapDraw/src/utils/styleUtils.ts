import { Style, Fill, Stroke, Circle, Text, Icon } from 'ol/style'
import type { ReferenceStyle, DrawStyle } from '../types'

/**
 * 默认样式配置
 */
export const DEFAULT_STYLES = {
  // 绘制时的样式
  drawing: {
    strokeColor: '#3b82f6',
    strokeWidth: 2,
    fillColor: 'rgba(59, 130, 246, 0.1)',
    pointRadius: 6
  },
  // 绘制完成的样式
  finished: {
    strokeColor: '#10b981',
    strokeWidth: 2,
    fillColor: 'rgba(16, 185, 129, 0.2)',
    pointRadius: 5
  },
  // 选中时的样式
  selected: {
    strokeColor: '#f59e0b',
    strokeWidth: 3,
    fillColor: 'rgba(245, 158, 11, 0.2)',
    pointRadius: 7
  },
  // 修改时的样式
  modifying: {
    strokeColor: '#ef4444',
    strokeWidth: 2,
    fillColor: 'rgba(239, 68, 68, 0.1)',
    pointRadius: 6
  },
  // 参考数据的默认样式
  reference: {
    strokeColor: '#6b7280',
    strokeWidth: 1,
    fillColor: 'rgba(107, 114, 128, 0.1)',
    pointRadius: 4
  }
}

/**
 * 样式工具类
 */
export class StyleUtils {
  /**
   * 创建基础样式
   * @param config 样式配置
   * @returns OpenLayers Style 对象
   */
  static createStyle(config: {
    strokeColor?: string
    strokeWidth?: number
    fillColor?: string
    pointRadius?: number
    opacity?: number
  }): Style {
    const {
      strokeColor = DEFAULT_STYLES.finished.strokeColor,
      strokeWidth = DEFAULT_STYLES.finished.strokeWidth,
      fillColor = DEFAULT_STYLES.finished.fillColor,
      pointRadius = DEFAULT_STYLES.finished.pointRadius,
      opacity = 1
    } = config

    return new Style({
      fill: new Fill({
        color: fillColor
      }),
      stroke: new Stroke({
        color: strokeColor,
        width: strokeWidth
      }),
      image: new Circle({
        radius: pointRadius,
        fill: new Fill({
          color: fillColor
        }),
        stroke: new Stroke({
          color: strokeColor,
          width: strokeWidth
        })
      })
    })
  }

  /**
   * 创建绘图相关的样式集合
   * @returns 绘图样式对象
   */
  static createDrawStyles(): DrawStyle {
    return {
      drawing: this.createStyle(DEFAULT_STYLES.drawing),
      finished: this.createStyle(DEFAULT_STYLES.finished),
      selected: this.createStyle(DEFAULT_STYLES.selected),
      modifying: this.createStyle(DEFAULT_STYLES.modifying)
    }
  }

  /**
   * 根据参考样式配置创建样式
   * @param styleConfig 参考样式配置
   * @returns OpenLayers Style 对象
   */
  static createReferenceStyle(styleConfig?: ReferenceStyle): Style {
    const config = {
      strokeColor: styleConfig?.strokeColor || DEFAULT_STYLES.reference.strokeColor,
      strokeWidth: styleConfig?.strokeWidth || DEFAULT_STYLES.reference.strokeWidth,
      fillColor: styleConfig?.fillColor || DEFAULT_STYLES.reference.fillColor,
      pointRadius: styleConfig?.pointRadius || DEFAULT_STYLES.reference.pointRadius,
      opacity: styleConfig?.opacity || 1
    }

    return this.createStyle(config)
  }

  /**
   * 创建文本样式
   * @param text 文本内容
   * @param config 文本样式配置
   * @returns OpenLayers Style 对象
   */
  static createTextStyle(text: string, config: {
    fontSize?: number
    fontFamily?: string
    color?: string
    offsetX?: number
    offsetY?: number
    strokeColor?: string
    strokeWidth?: number
  } = {}): Style {
    const {
      fontSize = 12,
      fontFamily = 'Arial',
      color = '#000000',
      offsetX = 0,
      offsetY = 0,
      strokeColor = '#ffffff',
      strokeWidth = 2
    } = config

    return new Style({
      text: new Text({
        text,
        font: `${fontSize}px ${fontFamily}`,
        fill: new Fill({
          color
        }),
        stroke: new Stroke({
          color: strokeColor,
          width: strokeWidth
        }),
        offsetX,
        offsetY
      })
    })
  }

  /**
   * 创建顶点样式（用于编辑时显示控制点）
   * @param config 样式配置
   * @returns OpenLayers Style 对象
   */
  static createVertexStyle(config: {
    radius?: number
    color?: string
    strokeColor?: string
    strokeWidth?: number
  } = {}): Style {
    const {
      radius = 4,
      color = '#ffffff',
      strokeColor = '#3b82f6',
      strokeWidth = 2
    } = config

    return new Style({
      image: new Circle({
        radius,
        fill: new Fill({
          color
        }),
        stroke: new Stroke({
          color: strokeColor,
          width: strokeWidth
        })
      }),
      geometry: function(feature) {
        // 只对多边形和线串显示顶点
        const geometry = feature.getGeometry()
        if (!geometry) return null
        
        const type = geometry.getType()
        if (type === 'Polygon') {
          return (geometry as any).getCoordinates()[0]
        } else if (type === 'LineString') {
          return (geometry as any).getCoordinates()
        }
        return null
      }
    })
  }

  /**
   * 根据几何类型获取默认样式
   * @param geometryType 几何类型
   * @param state 状态 ('drawing' | 'finished' | 'selected' | 'modifying')
   * @returns OpenLayers Style 对象
   */
  static getStyleByGeometryType(
    geometryType: string,
    state: 'drawing' | 'finished' | 'selected' | 'modifying' = 'finished'
  ): Style {
    const baseConfig = DEFAULT_STYLES[state]
    
    // 根据几何类型调整样式
    switch (geometryType) {
      case 'Point':
        return this.createStyle({
          ...baseConfig,
          pointRadius: baseConfig.pointRadius * 1.2
        })
      case 'LineString':
        return this.createStyle({
          ...baseConfig,
          strokeWidth: baseConfig.strokeWidth + 1,
          fillColor: 'transparent'
        })
      case 'Polygon':
        return this.createStyle(baseConfig)
      case 'Circle':
        return this.createStyle({
          ...baseConfig,
          strokeWidth: baseConfig.strokeWidth + 1
        })
      default:
        return this.createStyle(baseConfig)
    }
  }

  /**
   * 创建深色主题样式
   * @param baseStyle 基础样式配置
   * @returns 深色主题样式配置
   */
  static createDarkThemeStyle(baseStyle: any): any {
    return {
      strokeColor: this.adjustColorBrightness(baseStyle.strokeColor, 40),
      strokeWidth: baseStyle.strokeWidth,
      fillColor: this.adjustColorOpacity(baseStyle.fillColor, 0.1),
      pointRadius: baseStyle.pointRadius
    }
  }

  /**
   * 调整颜色亮度
   * @param color 颜色字符串
   * @param amount 调整量 (-100 到 100)
   * @returns 调整后的颜色
   */
  static adjustColorBrightness(color: string, amount: number): string {
    try {
      // 简单的颜色亮度调整实现
      const usePound = color[0] === '#'
      const col = usePound ? color.slice(1) : color
      
      if (col.length !== 6) return color
      
      const num = parseInt(col, 16)
      let r = (num >> 16) + amount
      let b = ((num >> 8) & 0x00FF) + amount
      let g = (num & 0x0000FF) + amount
      
      r = r > 255 ? 255 : r < 0 ? 0 : r
      b = b > 255 ? 255 : b < 0 ? 0 : b
      g = g > 255 ? 255 : g < 0 ? 0 : g
      
      return (usePound ? '#' : '') + (g | (b << 8) | (r << 16)).toString(16).padStart(6, '0')
    } catch (error) {
      return color
    }
  }

  /**
   * 调整颜色透明度
   * @param color 颜色字符串
   * @param opacity 新的透明度 (0-1)
   * @returns 调整后的颜色
   */
  static adjustColorOpacity(color: string, opacity: number): string {
    try {
      // 处理 rgba 格式
      if (color.startsWith('rgba')) {
        return color.replace(/[\d\.]+\)$/g, `${opacity})`)
      }
      
      // 处理 rgb 格式
      if (color.startsWith('rgb')) {
        return color.replace('rgb', 'rgba').replace(')', `, ${opacity})`)
      }
      
      // 处理十六进制格式
      if (color.startsWith('#')) {
        const hex = color.slice(1)
        if (hex.length === 6) {
          const r = parseInt(hex.slice(0, 2), 16)
          const g = parseInt(hex.slice(2, 4), 16)
          const b = parseInt(hex.slice(4, 6), 16)
          return `rgba(${r}, ${g}, ${b}, ${opacity})`
        }
      }
      
      return color
    } catch (error) {
      return color
    }
  }

  /**
   * 生成随机颜色
   * @param opacity 透明度
   * @returns 随机颜色字符串
   */
  static generateRandomColor(opacity: number = 1): string {
    const r = Math.floor(Math.random() * 256)
    const g = Math.floor(Math.random() * 256)
    const b = Math.floor(Math.random() * 256)
    return `rgba(${r}, ${g}, ${b}, ${opacity})`
  }

  /**
   * 根据主题获取样式集合
   * @param isDark 是否为深色主题
   * @returns 主题样式集合
   */
  static getThemeStyles(isDark: boolean = false) {
    const baseStyles = DEFAULT_STYLES
    
    if (!isDark) {
      return baseStyles
    }
    
    // 深色主题样式调整
    return {
      drawing: this.createDarkThemeStyle(baseStyles.drawing),
      finished: this.createDarkThemeStyle(baseStyles.finished),
      selected: this.createDarkThemeStyle(baseStyles.selected),
      modifying: this.createDarkThemeStyle(baseStyles.modifying),
      reference: this.createDarkThemeStyle(baseStyles.reference)
    }
  }
} 