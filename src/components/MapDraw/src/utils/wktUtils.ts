import { Feature } from 'ol'
import { Geometry, GeometryCollection } from 'ol/geom'
import WKT from 'ol/format/WKT'
import GeoJSON from 'ol/format/GeoJSON'

/**
 * WKT 格式转换工具类
 */
export class WKTUtils {
  private static wktFormat = new WKT()
  private static geoJSONFormat = new GeoJSON()

  /**
   * 将 WKT 字符串转换为 OpenLayers Feature
   * @param wkt WKT 字符串
   * @returns Feature 对象
   */
  static wktToFeature(wkt: string): Feature<Geometry> | null {
    try {
      if (!wkt || wkt.trim() === '') {
        return null
      }
      
      const geometry = this.wktFormat.readGeometry(wkt.trim())
      if (!geometry) {
        return null
      }
      
      return new Feature({ geometry })
    } catch (error) {
      console.warn('WKT 转换为 Feature 失败:', error)
      return null
    }
  }

  /**
   * 将 Feature 转换为 WKT 字符串
   * @param feature Feature 对象
   * @returns WKT 字符串
   */
  static featureToWKT(feature: Feature<Geometry>): string {
    try {
      const geometry = feature.getGeometry()
      if (!geometry) {
        return ''
      }
      
      return this.wktFormat.writeGeometry(geometry)
    } catch (error) {
      console.warn('Feature 转换为 WKT 失败:', error)
      return ''
    }
  }

  /**
   * 将 WKT 字符串数组转换为 Feature 数组
   * @param wktArray WKT 字符串数组
   * @returns Feature 数组
   */
  static wktArrayToFeatures(wktArray: string[]): Feature<Geometry>[] {
    if (!Array.isArray(wktArray)) {
      return []
    }

    return wktArray
      .map(wkt => this.wktToFeature(wkt))
      .filter((feature): feature is Feature<Geometry> => feature !== null)
  }

  /**
   * 将 Feature 数组转换为 WKT 字符串数组
   * @param features Feature 数组
   * @returns WKT 字符串数组
   */
  static featuresToWKTArray(features: Feature<Geometry>[]): string[] {
    if (!Array.isArray(features)) {
      return []
    }

    return features
      .map(feature => this.featureToWKT(feature))
      .filter(wkt => wkt !== '')
  }

  /**
   * 合并多个 WKT 字符串为一个 GEOMETRYCOLLECTION
   * @param wktArray WKT 字符串数组
   * @returns GEOMETRYCOLLECTION WKT 字符串
   */
  static mergeWKTToCollection(wktArray: string[]): string {
    try {
      if (!Array.isArray(wktArray) || wktArray.length === 0) {
        return ''
      }

      const geometries = wktArray
        .map(wkt => this.wktFormat.readGeometry(wkt.trim()))
        .filter(geometry => geometry !== null)

      if (geometries.length === 0) {
        return ''
      }

      if (geometries.length === 1) {
        return this.wktFormat.writeGeometry(geometries[0])
      }

      // 创建 GeometryCollection
      const collection = new GeometryCollection(geometries)
      return this.wktFormat.writeGeometry(collection)
    } catch (error) {
      console.warn('合并 WKT 为 GEOMETRYCOLLECTION 失败:', error)
      return ''
    }
  }

  /**
   * 将 GEOMETRYCOLLECTION WKT 分解为单个几何体 WKT 数组
   * @param collectionWKT GEOMETRYCOLLECTION WKT 字符串
   * @returns WKT 字符串数组
   */
  static splitCollectionWKT(collectionWKT: string): string[] {
    try {
      if (!collectionWKT || collectionWKT.trim() === '') {
        return []
      }

      const geometry = this.wktFormat.readGeometry(collectionWKT.trim())
      
      if (geometry.getType() !== 'GeometryCollection') {
        return [collectionWKT]
      }

      const collection = geometry as GeometryCollection
      const geometries = collection.getGeometries()

      return geometries.map(geom => this.wktFormat.writeGeometry(geom))
    } catch (error) {
      console.warn('分解 GEOMETRYCOLLECTION WKT 失败:', error)
      return []
    }
  }

  /**
   * 验证 WKT 字符串格式是否正确
   * @param wkt WKT 字符串
   * @returns 是否有效
   */
  static isValidWKT(wkt: string): boolean {
    try {
      if (!wkt || wkt.trim() === '') {
        return false
      }
      
      const geometry = this.wktFormat.readGeometry(wkt.trim())
      return geometry !== null
    } catch (error) {
      return false
    }
  }

  /**
   * 获取 WKT 几何类型
   * @param wkt WKT 字符串
   * @returns 几何类型
   */
  static getWKTGeometryType(wkt: string): string | null {
    try {
      if (!wkt || wkt.trim() === '') {
        return null
      }
      
      const geometry = this.wktFormat.readGeometry(wkt.trim())
      return geometry ? geometry.getType() : null
    } catch (error) {
      return null
    }
  }

  /**
   * 将 WKT 转换为 GeoJSON
   * @param wkt WKT 字符串
   * @returns GeoJSON 对象
   */
  static wktToGeoJSON(wkt: string): any {
    try {
      const feature = this.wktToFeature(wkt)
      if (!feature) {
        return null
      }
      
      return this.geoJSONFormat.writeFeature(feature)
    } catch (error) {
      console.warn('WKT 转换为 GeoJSON 失败:', error)
      return null
    }
  }

  /**
   * 将 GeoJSON 转换为 WKT
   * @param geoJSON GeoJSON 对象
   * @returns WKT 字符串
   */
  static geoJSONToWKT(geoJSON: any): string {
    try {
      if (!geoJSON) {
        return ''
      }
      
      const feature = this.geoJSONFormat.readFeature(geoJSON) as Feature<Geometry>
      return this.featureToWKT(feature)
    } catch (error) {
      console.warn('GeoJSON 转换为 WKT 失败:', error)
      return ''
    }
  }

  /**
   * 简化 WKT 几何（减少坐标点数量）
   * @param wkt WKT 字符串
   * @param tolerance 简化容差
   * @returns 简化后的 WKT 字符串
   */
  static simplifyWKT(wkt: string, tolerance: number = 0.001): string {
    try {
      const geometry = this.wktFormat.readGeometry(wkt.trim())
      if (!geometry) {
        return wkt
      }
      
      // 对于支持简化的几何类型进行简化
      if (geometry.getType() === 'LineString' || geometry.getType() === 'Polygon') {
        const simplified = geometry.simplify(tolerance)
        return this.wktFormat.writeGeometry(simplified)
      }
      
      return wkt
    } catch (error) {
      console.warn('WKT 简化失败:', error)
      return wkt
    }
  }
}

/**
 * 导出常用的工具函数
 */
export const {
  wktToFeature,
  featureToWKT,
  wktArrayToFeatures,
  featuresToWKTArray,
  mergeWKTToCollection,
  splitCollectionWKT,
  isValidWKT,
  getWKTGeometryType,
  wktToGeoJSON,
  geoJSONToWKT,
  simplifyWKT
} = WKTUtils 