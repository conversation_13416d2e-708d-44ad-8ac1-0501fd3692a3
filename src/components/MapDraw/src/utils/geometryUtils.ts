import { Feature } from 'ol'
import { Geometry, Point, LineString, Polygon, Circle } from 'ol/geom'
import { getArea, getLength } from 'ol/sphere'
import { transform } from 'ol/proj'
import type { GeometryType } from '../types'

/**
 * 几何图形工具类
 */
export class GeometryUtils {
  /**
   * 验证几何图形是否有效
   * @param geometry 几何图形对象
   * @returns 是否有效
   */
  static isValidGeometry(geometry: Geometry): boolean {
    try {
      if (!geometry) return false
      
      const type = geometry.getType()
      const coordinates = (geometry as any).getCoordinates()
      
      switch (type) {
        case 'Point':
          return Array.isArray(coordinates) && coordinates.length === 2
        case 'LineString':
          return Array.isArray(coordinates) && coordinates.length >= 2
        case 'Polygon':
          return Array.isArray(coordinates) && 
                 coordinates[0] && 
                 coordinates[0].length >= 4 &&
                 this.isPolygonClosed(coordinates[0])
        case 'Circle':
          return true // Circle 验证比较特殊，这里简化处理
        default:
          return false
      }
    } catch (error) {
      return false
    }
  }

  /**
   * 检查多边形是否封闭
   * @param coordinates 多边形坐标数组
   * @returns 是否封闭
   */
  static isPolygonClosed(coordinates: number[][]): boolean {
    if (coordinates.length < 4) return false
    
    const first = coordinates[0]
    const last = coordinates[coordinates.length - 1]
    
    return first[0] === last[0] && first[1] === last[1]
  }

  /**
   * 计算几何图形的面积
   * @param geometry 几何图形对象
   * @param projection 投影坐标系
   * @returns 面积（平方米）
   */
  static calculateArea(geometry: Geometry, projection: string = 'EPSG:3857'): number {
    try {
      const type = geometry.getType()
      
      if (type === 'Polygon') {
        // 如果是地理坐标系，先转换到投影坐标系
        let targetGeometry = geometry
        if (projection === 'EPSG:4326') {
          targetGeometry = geometry.clone().transform('EPSG:4326', 'EPSG:3857')
        }
        
        return getArea(targetGeometry as Polygon)
      } else if (type === 'Circle') {
        const circle = geometry as Circle
        const radius = circle.getRadius()
        return Math.PI * radius * radius
      }
      
      return 0
    } catch (error) {
      console.warn('计算面积失败:', error)
      return 0
    }
  }

  /**
   * 计算几何图形的长度
   * @param geometry 几何图形对象
   * @param projection 投影坐标系
   * @returns 长度（米）
   */
  static calculateLength(geometry: Geometry, projection: string = 'EPSG:3857'): number {
    try {
      const type = geometry.getType()
      
      if (type === 'LineString') {
        // 如果是地理坐标系，先转换到投影坐标系
        let targetGeometry = geometry
        if (projection === 'EPSG:4326') {
          targetGeometry = geometry.clone().transform('EPSG:4326', 'EPSG:3857')
        }
        
        return getLength(targetGeometry as LineString)
      } else if (type === 'Polygon') {
        // 计算多边形周长
        const polygon = geometry as Polygon
        const coordinates = polygon.getCoordinates()[0]
        let length = 0
        
        for (let i = 0; i < coordinates.length - 1; i++) {
          const line = new LineString([coordinates[i], coordinates[i + 1]])
          if (projection === 'EPSG:4326') {
            line.transform('EPSG:4326', 'EPSG:3857')
          }
          length += getLength(line)
        }
        
        return length
      } else if (type === 'Circle') {
        const circle = geometry as Circle
        const radius = circle.getRadius()
        return 2 * Math.PI * radius
      }
      
      return 0
    } catch (error) {
      console.warn('计算长度失败:', error)
      return 0
    }
  }

  /**
   * 获取几何图形的中心点
   * @param geometry 几何图形对象
   * @returns 中心点坐标
   */
  static getCentroid(geometry: Geometry): [number, number] | null {
    try {
      const type = geometry.getType()
      
      switch (type) {
        case 'Point':
          return (geometry as Point).getCoordinates() as [number, number]
        case 'LineString':
          const lineCoords = (geometry as LineString).getCoordinates()
          const midIndex = Math.floor(lineCoords.length / 2)
          return lineCoords[midIndex] as [number, number]
        case 'Polygon':
          const extent = geometry.getExtent()
          return [
            (extent[0] + extent[2]) / 2,
            (extent[1] + extent[3]) / 2
          ]
        case 'Circle':
          return (geometry as Circle).getCenter() as [number, number]
        default:
          return null
      }
    } catch (error) {
      console.warn('获取中心点失败:', error)
      return null
    }
  }

  /**
   * 获取几何图形的边界框
   * @param geometry 几何图形对象
   * @returns 边界框 [minX, minY, maxX, maxY]
   */
  static getBounds(geometry: Geometry): [number, number, number, number] | null {
    try {
      return geometry.getExtent() as [number, number, number, number]
    } catch (error) {
      console.warn('获取边界框失败:', error)
      return null
    }
  }

  /**
   * 坐标系转换
   * @param geometry 几何图形对象
   * @param sourceProj 源坐标系
   * @param targetProj 目标坐标系
   * @returns 转换后的几何图形
   */
  static transformGeometry(
    geometry: Geometry,
    sourceProj: string,
    targetProj: string
  ): Geometry {
    try {
      return geometry.clone().transform(sourceProj, targetProj)
    } catch (error) {
      console.warn('坐标系转换失败:', error)
      return geometry
    }
  }

  /**
   * 单点坐标转换
   * @param coordinate 坐标点 [x, y]
   * @param sourceProj 源坐标系
   * @param targetProj 目标坐标系
   * @returns 转换后的坐标
   */
  static transformCoordinate(
    coordinate: [number, number],
    sourceProj: string,
    targetProj: string
  ): [number, number] {
    try {
      return transform(coordinate, sourceProj, targetProj) as [number, number]
    } catch (error) {
      console.warn('坐标转换失败:', error)
      return coordinate
    }
  }

  /**
   * 简化几何图形（减少坐标点）
   * @param geometry 几何图形对象
   * @param tolerance 简化容差
   * @returns 简化后的几何图形
   */
  static simplifyGeometry(geometry: Geometry, tolerance: number = 0.001): Geometry {
    try {
      const type = geometry.getType()
      
      if (type === 'LineString' || type === 'Polygon') {
        return geometry.simplify(tolerance)
      }
      
      return geometry
    } catch (error) {
      console.warn('几何图形简化失败:', error)
      return geometry
    }
  }

  /**
   * 检查两个几何图形是否相交
   * @param geometry1 几何图形1
   * @param geometry2 几何图形2
   * @returns 是否相交
   */
  static intersects(geometry1: Geometry, geometry2: Geometry): boolean {
    try {
      return geometry1.intersectsExtent(geometry2.getExtent())
    } catch (error) {
      console.warn('相交检测失败:', error)
      return false
    }
  }

  /**
   * 计算两点之间的距离
   * @param coord1 坐标点1
   * @param coord2 坐标点2
   * @param projection 投影坐标系
   * @returns 距离（米）
   */
  static calculateDistance(
    coord1: [number, number],
    coord2: [number, number],
    projection: string = 'EPSG:3857'
  ): number {
    try {
      const line = new LineString([coord1, coord2])
      
      if (projection === 'EPSG:4326') {
        line.transform('EPSG:4326', 'EPSG:3857')
      }
      
      return getLength(line)
    } catch (error) {
      console.warn('距离计算失败:', error)
      return 0
    }
  }

  /**
   * 格式化面积显示
   * @param area 面积（平方米）
   * @returns 格式化的面积字符串
   */
  static formatArea(area: number): string {
    if (area > 1000000) {
      return `${(area / 1000000).toFixed(2)} km²`
    } else if (area > 10000) {
      return `${(area / 10000).toFixed(2)} 公顷`
    } else {
      return `${area.toFixed(2)} m²`
    }
  }

  /**
   * 格式化长度显示
   * @param length 长度（米）
   * @returns 格式化的长度字符串
   */
  static formatLength(length: number): string {
    if (length > 1000) {
      return `${(length / 1000).toFixed(2)} km`
    } else {
      return `${length.toFixed(2)} m`
    }
  }

  /**
   * 根据几何类型创建空的几何对象
   * @param type 几何类型
   * @returns 几何对象
   */
  static createEmptyGeometry(type: GeometryType): Geometry | null {
    try {
      switch (type) {
        case 'Point':
          return new Point([0, 0])
        case 'LineString':
          return new LineString([[0, 0], [0, 0]])
        case 'Polygon':
          return new Polygon([[[0, 0], [0, 0], [0, 0], [0, 0]]])
        case 'Circle':
          return new Circle([0, 0], 1000)
        default:
          return null
      }
    } catch (error) {
      console.warn('创建空几何对象失败:', error)
      return null
    }
  }

  /**
   * 检查几何图形是否为空
   * @param geometry 几何图形对象
   * @returns 是否为空
   */
  static isEmpty(geometry: Geometry): boolean {
    try {
      // 检查坐标是否为空或无效
      const coords = (geometry as any).getCoordinates()
      
      if (!coords) return true
      
      const type = geometry.getType()
      switch (type) {
        case 'Point':
          return !Array.isArray(coords) || coords.length !== 2
        case 'LineString':
          return !Array.isArray(coords) || coords.length < 2
        case 'Polygon':
          return !Array.isArray(coords) || !coords[0] || coords[0].length < 4
        default:
          return false
      }
    } catch (error) {
      return true
    }
  }

  /**
   * 获取几何图形的坐标统计信息
   * @param geometry 几何图形对象
   * @returns 统计信息对象
   */
  static getGeometryStats(geometry: Geometry): {
    type: string
    pointCount: number
    area?: number
    length?: number
    centroid?: [number, number]
    bounds?: [number, number, number, number]
  } {
    const type = geometry.getType()
    const stats: any = {
      type,
      pointCount: 0
    }

    try {
      // 计算坐标点数量
      const coords = (geometry as any).getCoordinates()
      if (type === 'Point') {
        stats.pointCount = 1
      } else if (type === 'LineString') {
        stats.pointCount = coords ? coords.length : 0
      } else if (type === 'Polygon') {
        stats.pointCount = coords && coords[0] ? coords[0].length : 0
      }

      // 计算面积和长度
      if (type === 'Polygon' || type === 'Circle') {
        stats.area = this.calculateArea(geometry)
      }
      
      if (type === 'LineString' || type === 'Polygon') {
        stats.length = this.calculateLength(geometry)
      }

      // 获取中心点和边界框
      stats.centroid = this.getCentroid(geometry)
      stats.bounds = this.getBounds(geometry)

    } catch (error) {
      console.warn('获取几何图形统计信息失败:', error)
    }

    return stats
  }
}

/**
 * 导出常用的工具函数
 */
export const {
  isValidGeometry,
  calculateArea,
  calculateLength,
  getCentroid,
  getBounds,
  transformGeometry,
  transformCoordinate,
  simplifyGeometry,
  intersects,
  calculateDistance,
  formatArea,
  formatLength,
  createEmptyGeometry,
  isEmpty,
  getGeometryStats
} = GeometryUtils 