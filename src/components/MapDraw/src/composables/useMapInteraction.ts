import { ref, computed, reactive, watch, type Ref } from 'vue'
import { Map as OlMap } from 'ol'
import { Feature } from 'ol'
import { Geometry } from 'ol/geom'
import { DrawMode, type ToolbarConfig, type ToolButton } from '../types'
import { GeometryUtils } from '../utils/geometryUtils'

/**
 * 地图交互控制 Composable
 */
export function useMapInteraction(
  map: Ref<OlMap | null>,
  interactionState: Ref<any>,
  emit?: (event: string, ...args: any[]) => void
) {
  // 工具栏配置
  const toolbarConfig = ref<ToolbarConfig>({
    showDrawTools: true,
    showEditTools: true,
    showClearButton: true,
    showUndoRedo: true,
    customTools: []
  })

  // 工具栏状态
  const toolbarState = reactive({
    activeMode: DrawMode.NONE,
    isVisible: true,
    isCollapsed: false,
    position: 'top-left' as 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  })

  // 右键菜单状态
  const contextMenu = reactive({
    visible: false,
    x: 0,
    y: 0,
    targetFeature: null as Feature<Geometry> | null,
    menuItems: [] as Array<{
      label: string
      icon?: string
      action: () => void
      disabled?: boolean
    }>
  })

  // 提示信息
  const tooltip = reactive({
    visible: false,
    x: 0,
    y: 0,
    content: '',
    type: 'info' as 'info' | 'warning' | 'error' | 'success'
  })

  // 默认绘制工具按钮
  const defaultDrawTools: ToolButton[] = [
    {
      id: 'point',
      title: '绘制点',
      icon: 'el-icon-location',
      onClick: () => activateDrawTool(DrawMode.POINT)
    },
    {
      id: 'line',
      title: '绘制线',
      icon: 'el-icon-minus',
      onClick: () => activateDrawTool(DrawMode.LINE)
    },
    {
      id: 'polygon',
      title: '绘制面',
      icon: 'el-icon-crop',
      onClick: () => activateDrawTool(DrawMode.POLYGON)
    },
    {
      id: 'circle',
      title: '绘制圆',
      icon: 'el-icon-circle-plus',
      onClick: () => activateDrawTool(DrawMode.CIRCLE)
    }
  ]

  // 默认编辑工具按钮
  const defaultEditTools: ToolButton[] = [
    {
      id: 'select',
      title: '选择编辑',
      icon: 'el-icon-mouse',
      onClick: () => activateEditMode()
    }
  ]

  // 默认操作工具按钮
  const defaultActionTools: ToolButton[] = [
    {
      id: 'undo',
      title: '撤销',
      icon: 'el-icon-refresh-left',
      onClick: () => emit?.('undo'),
      get disabled() {
        return !interactionState.value?.canUndo
      }
    },
    {
      id: 'redo',
      title: '重做',
      icon: 'el-icon-refresh-right',
      onClick: () => emit?.('redo'),
      get disabled() {
        return !interactionState.value?.canRedo
      }
    },
    {
      id: 'clear',
      title: '清空',
      icon: 'el-icon-delete',
      onClick: () => handleClearAll()
    }
  ]

  /**
   * 激活绘制工具
   */
  const activateDrawTool = (mode: DrawMode) => {
    toolbarState.activeMode = mode
    emit?.('activate-draw-mode', mode)
    
    // 显示绘制提示
    showTooltip(`正在绘制${getDrawModeLabel(mode)}，点击地图开始绘制`, 'info', 3000)
  }

  /**
   * 激活编辑模式
   */
  const activateEditMode = () => {
    toolbarState.activeMode = DrawMode.NONE
    emit?.('activate-edit-mode')
    
    // 显示编辑提示
    showTooltip('已切换到编辑模式，点击要素进行编辑', 'info', 2000)
  }

  /**
   * 获取绘制模式标签
   */
  const getDrawModeLabel = (mode: DrawMode): string => {
    const labels = {
      [DrawMode.POINT]: '点',
      [DrawMode.LINE]: '线',
      [DrawMode.POLYGON]: '面',
      [DrawMode.CIRCLE]: '圆',
      [DrawMode.NONE]: ''
    }
    return labels[mode] || ''
  }

  /**
   * 显示工具提示
   */
  const showTooltip = (content: string, type: 'info' | 'warning' | 'error' | 'success' = 'info', duration: number = 0) => {
    tooltip.content = content
    tooltip.type = type
    tooltip.visible = true
    
    if (duration > 0) {
      setTimeout(() => {
        hideTooltip()
      }, duration)
    }
  }

  /**
   * 隐藏工具提示
   */
  const hideTooltip = () => {
    tooltip.visible = false
    tooltip.content = ''
  }

  /**
   * 显示右键菜单
   */
  const showContextMenu = (event: MouseEvent, feature?: Feature<Geometry>) => {
    event.preventDefault()
    
    contextMenu.x = event.clientX
    contextMenu.y = event.clientY
    contextMenu.targetFeature = feature || null
    contextMenu.menuItems = generateContextMenuItems(feature)
    contextMenu.visible = true

    // 添加点击事件监听，点击其他地方时隐藏菜单
    const hideMenu = () => {
      contextMenu.visible = false
      document.removeEventListener('click', hideMenu)
    }
    
    setTimeout(() => {
      document.addEventListener('click', hideMenu)
    }, 0)
  }

  /**
   * 生成右键菜单项
   */
  const generateContextMenuItems = (feature?: Feature<Geometry>) => {
    const items: Array<{
      label: string
      icon?: string
      action: () => void
      disabled?: boolean
    }> = []

    if (feature) {
      // 有选中要素时的菜单
      items.push(
        {
          label: '编辑要素',
          icon: 'el-icon-edit',
          action: () => emit?.('edit-feature', feature)
        },
        {
          label: '删除要素',
          icon: 'el-icon-delete',
          action: () => handleDeleteFeature(feature)
        },
        {
          label: '复制要素',
          icon: 'el-icon-copy-document',
          action: () => handleCopyFeature(feature)
        },
        {
          label: '查看属性',
          icon: 'el-icon-view',
          action: () => showFeatureProperties(feature)
        }
      )
    } else {
      // 无选中要素时的菜单
      items.push(
        {
          label: '绘制点',
          icon: 'el-icon-location',
          action: () => activateDrawTool(DrawMode.POINT)
        },
        {
          label: '绘制线',
          icon: 'el-icon-minus',
          action: () => activateDrawTool(DrawMode.LINE)
        },
        {
          label: '绘制面',
          icon: 'el-icon-crop',
          action: () => activateDrawTool(DrawMode.POLYGON)
        },
        {
          label: '粘贴要素',
          icon: 'el-icon-document-copy',
          action: () => handlePasteFeature(),
          disabled: !clipboardFeature.value
        }
      )
    }

    return items
  }

  // 剪贴板功能
  const clipboardFeature = ref<Feature<Geometry> | null>(null)

  /**
   * 复制要素
   */
  const handleCopyFeature = (feature: Feature<Geometry>) => {
    clipboardFeature.value = feature.clone()
    showTooltip('要素已复制到剪贴板', 'success', 2000)
  }

  /**
   * 粘贴要素
   */
  const handlePasteFeature = () => {
    if (clipboardFeature.value) {
      emit?.('paste-feature', clipboardFeature.value.clone())
      showTooltip('要素已粘贴', 'success', 2000)
    }
  }

  /**
   * 删除要素
   */
  const handleDeleteFeature = (feature: Feature<Geometry>) => {
    emit?.('delete-feature', feature)
    showTooltip('要素已删除', 'success', 2000)
  }

  /**
   * 清空所有要素
   */
  const handleClearAll = () => {
    // 显示确认对话框
    if (confirm('确定要清空所有绘制内容吗？此操作不可恢复。')) {
      emit?.('clear-all')
      showTooltip('已清空所有绘制内容', 'success', 2000)
    }
  }

  /**
   * 显示要素属性
   */
  const showFeatureProperties = (feature: Feature<Geometry>) => {
    const geometry = feature.getGeometry()
    if (!geometry) return

    const stats = GeometryUtils.getGeometryStats(geometry)
    const properties = {
      类型: stats.type,
      坐标点数: stats.pointCount,
      面积: stats.area ? GeometryUtils.formatArea(stats.area) : '不适用',
      长度: stats.length ? GeometryUtils.formatLength(stats.length) : '不适用',
      中心点: stats.centroid ? `[${stats.centroid[0].toFixed(6)}, ${stats.centroid[1].toFixed(6)}]` : '不适用'
    }

    // 触发属性显示事件
    emit?.('show-feature-properties', { feature, properties })
  }

  /**
   * 设置工具栏配置
   */
  const setToolbarConfig = (config: Partial<ToolbarConfig>) => {
    Object.assign(toolbarConfig.value, config)
  }

  /**
   * 切换工具栏可见性
   */
  const toggleToolbar = () => {
    toolbarState.isVisible = !toolbarState.isVisible
  }

  /**
   * 切换工具栏折叠状态
   */
  const toggleToolbarCollapse = () => {
    toolbarState.isCollapsed = !toolbarState.isCollapsed
  }

  /**
   * 设置工具栏位置
   */
  const setToolbarPosition = (position: typeof toolbarState.position) => {
    toolbarState.position = position
  }

  // 计算属性
  const drawTools = computed(() => {
    if (!toolbarConfig.value.showDrawTools) return []
    return defaultDrawTools
  })

  const editTools = computed(() => {
    if (!toolbarConfig.value.showEditTools) return []
    return defaultEditTools
  })

  const actionTools = computed(() => {
    const tools: ToolButton[] = []
    
    if (toolbarConfig.value.showUndoRedo) {
      tools.push(...defaultActionTools.filter(tool => tool.id === 'undo' || tool.id === 'redo'))
    }
    
    if (toolbarConfig.value.showClearButton) {
      tools.push(...defaultActionTools.filter(tool => tool.id === 'clear'))
    }
    
    return tools
  })

  const allTools = computed(() => {
    return [
      ...drawTools.value,
      ...editTools.value,
      ...actionTools.value,
      ...(toolbarConfig.value.customTools || [])
    ]
  })

  const activeToolId = computed(() => {
    switch (toolbarState.activeMode) {
      case DrawMode.POINT:
        return 'point'
      case DrawMode.LINE:
        return 'line'
      case DrawMode.POLYGON:
        return 'polygon'
      case DrawMode.CIRCLE:
        return 'circle'
      default:
        return 'select'
    }
  })

  // 监听交互状态变化
  watch(() => interactionState.value?.currentMode, (newMode) => {
    if (newMode !== undefined) {
      toolbarState.activeMode = newMode
    }
  })

  // 键盘快捷键处理
  const handleKeyboardShortcuts = (event: KeyboardEvent) => {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key.toLowerCase()) {
        case 'z':
          event.preventDefault()
          if (event.shiftKey) {
            emit?.('redo')
          } else {
            emit?.('undo')
          }
          break
        case 'c':
          if (interactionState.value?.selectedFeature) {
            event.preventDefault()
            handleCopyFeature(interactionState.value.selectedFeature)
          }
          break
        case 'v':
          if (clipboardFeature.value) {
            event.preventDefault()
            handlePasteFeature()
          }
          break
      }
    }

    // 数字键快速选择工具
    if (!event.ctrlKey && !event.metaKey && !event.shiftKey) {
      switch (event.key) {
        case '1':
          activateDrawTool(DrawMode.POINT)
          break
        case '2':
          activateDrawTool(DrawMode.LINE)
          break
        case '3':
          activateDrawTool(DrawMode.POLYGON)
          break
        case '4':
          activateDrawTool(DrawMode.CIRCLE)
          break
        case '0':
          activateEditMode()
          break
      }
    }
  }

  // 监听地图变化
  watch(map, (newMap, oldMap) => {
    if (oldMap) {
      document.removeEventListener('keydown', handleKeyboardShortcuts)
    }
    
    if (newMap) {
      document.addEventListener('keydown', handleKeyboardShortcuts)
      
      // 绑定地图右键事件
      newMap.getViewport().addEventListener('contextmenu', (event) => {
        // 检查是否点击在要素上
        const features = newMap.getFeaturesAtPixel(newMap.getEventPixel(event))
        const feature = features && features.length > 0 ? features[0] as Feature<Geometry> : undefined
        
        showContextMenu(event, feature)
      })
    }
  }, { immediate: true })

  return {
    // 状态
    toolbarConfig,
    toolbarState,
    contextMenu,
    tooltip,
    
    // 工具方法
    activateDrawTool,
    activateEditMode,
    showTooltip,
    hideTooltip,
    showContextMenu,
    setToolbarConfig,
    toggleToolbar,
    toggleToolbarCollapse,
    setToolbarPosition,
    
    // 要素操作
    handleCopyFeature,
    handlePasteFeature,
    handleDeleteFeature,
    handleClearAll,
    showFeatureProperties,
    
    // 计算属性
    drawTools,
    editTools,
    actionTools,
    allTools,
    activeToolId,
    
    // 剪贴板
    clipboardFeature
  }
} 