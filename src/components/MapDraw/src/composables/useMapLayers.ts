import { ref, computed, watch, onUnmounted, readonly, type Ref } from 'vue'
import { Map as OlMap } from 'ol'
import VectorLayer from 'ol/layer/Vector'
import VectorSource from 'ol/source/Vector'
import { Feature } from 'ol'
import { Geometry } from 'ol/geom'
import { Collection } from 'ol'
import type { LayerConfig, ReferenceStyle } from '../types'
import { WKTUtils } from '../utils/wktUtils'
import { StyleUtils } from '../utils/styleUtils'

/**
 * 图层管理 Composable
 */
export function useMapLayers(map: Ref<OlMap | null>) {
  // 图层集合
  const drawLayer = ref<any>(null)
  const referenceLayer = ref<any>(null)
  const layers = ref<Map<string, any>>(new Map())

  // 图层数据源
  const drawSource = ref<any>(null)
  const referenceSource = ref<any>(null)

  // 图层配置
  const layerConfigs = ref<Map<string, LayerConfig>>(new Map())

  /**
   * 初始化图层
   */
  const initializeLayers = () => {
    if (!map.value) return

    // 创建绘制图层
    createDrawLayer()
    
    // 创建参考图层
    createReferenceLayer()
  }

  /**
   * 创建绘制图层
   */
  const createDrawLayer = () => {
    if (!map.value) return

    // 创建绘制数据源
    drawSource.value = new VectorSource({
      features: new Collection<Feature<Geometry>>()
    })

    // 创建绘制图层
    drawLayer.value = new VectorLayer({
      source: drawSource.value,
      style: StyleUtils.getStyleByGeometryType('Polygon', 'finished'),
      zIndex: 100 // 确保绘制图层在最上层
    })

    // 添加到地图
    map.value.addLayer(drawLayer.value)
    
    // 注册图层
    layers.value.set('draw', drawLayer.value)
    layerConfigs.value.set('draw', {
      name: 'draw',
      type: 'draw',
      visible: true,
      opacity: 1,
      style: StyleUtils.createDrawStyles().finished
    })
  }

  /**
   * 创建参考图层
   */
  const createReferenceLayer = () => {
    if (!map.value) return

    // 创建参考数据源
    referenceSource.value = new VectorSource({
      features: new Collection<Feature<Geometry>>()
    })

    // 创建参考图层
    referenceLayer.value = new VectorLayer({
      source: referenceSource.value,
      style: StyleUtils.createReferenceStyle(),
      zIndex: 50 // 参考图层在绘制图层下方
    })

    // 添加到地图
    map.value.addLayer(referenceLayer.value)
    
    // 注册图层
    layers.value.set('reference', referenceLayer.value)
    layerConfigs.value.set('reference', {
      name: 'reference',
      type: 'reference',
      visible: true,
      opacity: 0.7,
      style: StyleUtils.createReferenceStyle()
    })
  }

  /**
   * 创建自定义图层
   */
  const createCustomLayer = (config: LayerConfig): any => {
    if (!map.value) throw new Error('地图未初始化')

    const source = new VectorSource({
      features: new Collection<Feature<Geometry>>()
    })

    const layer = new VectorLayer({
      source,
      style: config.style || StyleUtils.createReferenceStyle(),
      visible: config.visible !== false,
      opacity: config.opacity || 1,
      zIndex: config.type === 'draw' ? 100 : 50
    })

    map.value.addLayer(layer)
    layers.value.set(config.name, layer)
    layerConfigs.value.set(config.name, config)

    return layer
  }

  /**
   * 移除图层
   */
  const removeLayer = (layerName: string) => {
    if (!map.value) return

    const layer = layers.value.get(layerName)
    if (layer) {
      map.value.removeLayer(layer)
      layers.value.delete(layerName)
      layerConfigs.value.delete(layerName)
    }
  }

  /**
   * 获取图层
   */
  const getLayer = (layerName: string): any => {
    return layers.value.get(layerName) || null
  }

  /**
   * 设置图层可见性
   */
  const setLayerVisible = (layerName: string, visible: boolean) => {
    const layer = layers.value.get(layerName)
    if (layer) {
      layer.setVisible(visible)
      
      const config = layerConfigs.value.get(layerName)
      if (config) {
        config.visible = visible
      }
    }
  }

  /**
   * 设置图层透明度
   */
  const setLayerOpacity = (layerName: string, opacity: number) => {
    const layer = layers.value.get(layerName)
    if (layer) {
      layer.setOpacity(opacity)
      
      const config = layerConfigs.value.get(layerName)
      if (config) {
        config.opacity = opacity
      }
    }
  }

  /**
   * 设置图层样式
   */
  const setLayerStyle = (layerName: string, style: any) => {
    const layer = layers.value.get(layerName)
    if (layer) {
      layer.setStyle(style)
      
      const config = layerConfigs.value.get(layerName)
      if (config) {
        config.style = style
      }
    }
  }

  /**
   * 添加要素到绘制图层
   */
  const addFeatureToDraw = (feature: Feature<Geometry>) => {
    if (drawSource.value) {
      drawSource.value.addFeature(feature)
    }
  }

  /**
   * 从绘制图层移除要素
   */
  const removeFeatureFromDraw = (feature: Feature<Geometry>) => {
    if (drawSource.value) {
      drawSource.value.removeFeature(feature)
    }
  }

  /**
   * 清空绘制图层
   */
  const clearDrawLayer = () => {
    if (drawSource.value) {
      drawSource.value.clear()
    }
  }

  /**
   * 获取绘制图层的所有要素
   */
  const getDrawFeatures = (): Feature<Geometry>[] => {
    if (!drawSource.value) return []
    return drawSource.value.getFeatures()
  }

  /**
   * 加载参考数据
   */
  const loadReferenceData = (wktArray: string[], styles?: ReferenceStyle[]) => {
    if (!referenceSource.value) return

    // 清空现有参考数据
    referenceSource.value.clear()

    // 转换 WKT 数据为要素
    const features = WKTUtils.wktArrayToFeatures(wktArray)
    
    // 应用样式
    features.forEach((feature, index) => {
      if (styles && styles[index]) {
        const style = StyleUtils.createReferenceStyle(styles[index])
        feature.setStyle(style)
      }
    })

    // 添加要素到参考图层
    referenceSource.value.addFeatures(features)
  }

  /**
   * 清空参考图层
   */
  const clearReferenceLayer = () => {
    if (referenceSource.value) {
      referenceSource.value.clear()
    }
  }

  /**
   * 设置绘制图层数据
   */
  const setDrawLayerData = (wkt: string) => {
    if (!drawSource.value) return

    // 清空现有数据
    drawSource.value.clear()

    if (wkt && wkt.trim()) {
      const feature = WKTUtils.wktToFeature(wkt)
      if (feature) {
        drawSource.value.addFeature(feature)
      }
    }
  }

  /**
   * 获取绘制图层数据（WKT 格式）
   */
  const getDrawLayerData = (): string => {
    const features = getDrawFeatures()
    if (features.length === 0) return ''
    
    if (features.length === 1) {
      return WKTUtils.featureToWKT(features[0])
    }
    
    // 多个要素合并为 GEOMETRYCOLLECTION
    const wktArray = WKTUtils.featuresToWKTArray(features)
    return WKTUtils.mergeWKTToCollection(wktArray)
  }

  /**
   * 图层统计信息
   */
  const layerStats = computed(() => {
    const stats = {
      totalLayers: layers.value.size,
      drawFeatureCount: getDrawFeatures().length,
      referenceFeatureCount: referenceSource.value?.getFeatures().length || 0,
      visibleLayers: 0,
      hiddenLayers: 0
    }

    layerConfigs.value.forEach(config => {
      if (config.visible) {
        stats.visibleLayers++
      } else {
        stats.hiddenLayers++
      }
    })

    return stats
  })

  /**
   * 图层列表
   */
  const layerList = computed(() => {
    return Array.from(layerConfigs.value.values())
  })

  /**
   * 重新排序图层
   */
  const reorderLayers = () => {
    layerConfigs.value.forEach((config, name) => {
      const layer = layers.value.get(name)
      if (layer) {
        const zIndex = config.type === 'draw' ? 100 : 
                     config.type === 'reference' ? 50 : 25
        layer.setZIndex(zIndex)
      }
    })
  }

  /**
   * 适应图层范围
   */
  const fitToLayers = (layerNames?: string[], padding?: number[]) => {
    if (!map.value) return

    const targetLayers = layerNames 
      ? layerNames.map(name => layers.value.get(name)).filter(Boolean)
      : Array.from(layers.value.values())

    if (targetLayers.length === 0) return

    // 计算所有图层的总范围
    let extent: [number, number, number, number] | null = null

    targetLayers.forEach(layer => {
      const layerExtent = layer?.getSource()?.getExtent()
      if (layerExtent && layerExtent.every(coord => isFinite(coord))) {
        if (!extent) {
          extent = [...layerExtent] as [number, number, number, number]
        } else {
          extent[0] = Math.min(extent[0], layerExtent[0])
          extent[1] = Math.min(extent[1], layerExtent[1])
          extent[2] = Math.max(extent[2], layerExtent[2])
          extent[3] = Math.max(extent[3], layerExtent[3])
        }
      }
    })

    if (extent) {
      map.value.getView().fit(extent, {
        padding: padding || [20, 20, 20, 20],
        duration: 500
      })
    }
  }

  // 监听地图变化，自动初始化图层
  watch(map, (newMap) => {
    if (newMap) {
      initializeLayers()
    }
  }, { immediate: true })

  // 清理资源
  onUnmounted(() => {
    if (map.value) {
      layers.value.forEach(layer => {
        map.value!.removeLayer(layer)
      })
    }
    layers.value.clear()
    layerConfigs.value.clear()
  })

  return {
    // 图层引用
    drawLayer: readonly(drawLayer),
    referenceLayer: readonly(referenceLayer),
    layers: readonly(layers),
    
    // 数据源引用
    drawSource: readonly(drawSource),
    referenceSource: readonly(referenceSource),
    
    // 图层管理方法
    initializeLayers,
    createCustomLayer,
    removeLayer,
    getLayer,
    
    // 图层配置方法
    setLayerVisible,
    setLayerOpacity,
    setLayerStyle,
    
    // 绘制图层操作
    addFeatureToDraw,
    removeFeatureFromDraw,
    clearDrawLayer,
    getDrawFeatures,
    setDrawLayerData,
    getDrawLayerData,
    
    // 参考图层操作
    loadReferenceData,
    clearReferenceLayer,
    
    // 工具方法
    reorderLayers,
    fitToLayers,
    
    // 计算属性
    layerStats,
    layerList
  }
} 