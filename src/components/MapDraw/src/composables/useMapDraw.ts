import { ref, computed, watch, nextTick, readonly, type Ref } from 'vue'
import { Map as OlMap } from 'ol'
import { Draw, Modify, Select, Snap } from 'ol/interaction'
import { Feature } from 'ol'
import { Geometry } from 'ol/geom'
import { click } from 'ol/events/condition'
import { DrawMode, type MapInteractionState, type HistoryRecord } from '../types'
import { WKTUtils } from '../utils/wktUtils'
import { StyleUtils } from '../utils/styleUtils'

/**
 * 绘图逻辑 Composable
 */
export function useMapDraw(
  map: Ref<OlMap | null>,
  drawSource: Ref<any>,
  emit?: (event: string, ...args: any[]) => void
) {
  // 交互状态
  const interactionState = ref<MapInteractionState>({
    currentMode: DrawMode.NONE,
    isDrawing: false,
    isEditing: false,
    selectedFeature: null,
    canUndo: false,
    canRedo: false
  })

  // 绘制交互
  const drawInteraction = ref<any>(null)
  const modifyInteraction = ref<any>(null)
  const selectInteraction = ref<any>(null)
  const snapInteraction = ref<any>(null)

  // 历史记录
  const history = ref<HistoryRecord[]>([])
  const historyIndex = ref(-1)
  const maxHistorySize = 50

  // 样式配置
  const drawStyles = StyleUtils.createDrawStyles()

  /**
   * 激活绘制模式
   */
  const activateDrawMode = (mode: DrawMode) => {
    if (!map.value || !drawSource.value) return

    // 清除现有交互
    clearInteractions()

    if (mode === DrawMode.NONE) {
      interactionState.value.currentMode = DrawMode.NONE
      return
    }

    // 创建绘制交互
    const geometryType = mode === DrawMode.CIRCLE ? 'Circle' : mode
    
    drawInteraction.value = new Draw({
      source: drawSource.value,
      type: geometryType as any,
      style: drawStyles.drawing
    })

    // 绑定绘制事件
    drawInteraction.value.on('drawstart', (event: any) => {
      interactionState.value.isDrawing = true
      emit?.('draw-start', { mode, feature: event.feature })
    })

    drawInteraction.value.on('drawend', (event: any) => {
      interactionState.value.isDrawing = false
      
      const wkt = WKTUtils.featureToWKT(event.feature)
      
      // 记录历史
      addHistoryRecord('add', `添加${mode}图形`)
      
      emit?.('draw-end', { mode, feature: event.feature, wkt })
      
      // 自动切换到编辑模式
      nextTick(() => {
        activateEditMode()
      })
    })

    // 添加到地图
    map.value.addInteraction(drawInteraction.value)

    // 添加捕捉交互
    addSnapInteraction()

    interactionState.value.currentMode = mode
  }

  /**
   * 激活编辑模式
   */
  const activateEditMode = () => {
    if (!map.value || !drawSource.value) return

    // 清除绘制交互
    clearDrawInteraction()

    // 创建选择交互
    selectInteraction.value = new Select({
      condition: click,
      style: drawStyles.selected,
      layers: (layer: any) => layer.getSource() === drawSource.value
    })

    // 创建修改交互
    modifyInteraction.value = new Modify({
      features: selectInteraction.value.getFeatures(),
      style: drawStyles.modifying
    })

    // 绑定选择事件
    selectInteraction.value.on('select', (event: any) => {
      const feature = event.selected[0] || null
      interactionState.value.selectedFeature = feature
      
      const wkt = feature ? WKTUtils.featureToWKT(feature) : undefined
      emit?.('feature-select', { feature, wkt })
    })

    // 绑定修改事件
    modifyInteraction.value.on('modifystart', () => {
      interactionState.value.isEditing = true
    })

    modifyInteraction.value.on('modifyend', (event: any) => {
      interactionState.value.isEditing = false
      
      const feature = event.features.getArray()[0]
      if (feature) {
        const wkt = WKTUtils.featureToWKT(feature)
        
        // 记录历史
        addHistoryRecord('modify', '修改图形')
        
        emit?.('feature-modify', { feature, wkt })
      }
    })

    // 添加到地图
    map.value.addInteraction(selectInteraction.value)
    map.value.addInteraction(modifyInteraction.value)

    // 添加捕捉交互
    addSnapInteraction()

    interactionState.value.currentMode = DrawMode.NONE
  }

  /**
   * 添加捕捉交互
   */
  const addSnapInteraction = () => {
    if (!map.value || !drawSource.value) return

    snapInteraction.value = new Snap({
      source: drawSource.value
    })

    map.value.addInteraction(snapInteraction.value)
  }

  /**
   * 清除所有交互
   */
  const clearInteractions = () => {
    if (!map.value) return

    // 清除绘制交互
    clearDrawInteraction()
    
    // 清除编辑交互
    clearEditInteractions()
    
    // 清除捕捉交互
    clearSnapInteraction()
  }

  /**
   * 清除绘制交互
   */
  const clearDrawInteraction = () => {
    if (drawInteraction.value && map.value) {
      map.value.removeInteraction(drawInteraction.value)
      drawInteraction.value = null
    }
  }

  /**
   * 清除编辑交互
   */
  const clearEditInteractions = () => {
    if (selectInteraction.value && map.value) {
      map.value.removeInteraction(selectInteraction.value)
      selectInteraction.value = null
    }
    
    if (modifyInteraction.value && map.value) {
      map.value.removeInteraction(modifyInteraction.value)
      modifyInteraction.value = null
    }
    
    interactionState.value.selectedFeature = null
  }

  /**
   * 清除捕捉交互
   */
  const clearSnapInteraction = () => {
    if (snapInteraction.value && map.value) {
      map.value.removeInteraction(snapInteraction.value)
      snapInteraction.value = null
    }
  }

  /**
   * 删除选中的要素
   */
  const deleteSelectedFeature = () => {
    if (!interactionState.value.selectedFeature || !drawSource.value) return

    const feature = interactionState.value.selectedFeature as any
    const wkt = WKTUtils.featureToWKT(feature)
    
    // 从数据源移除
    drawSource.value.removeFeature(feature)
    
    // 记录历史
    addHistoryRecord('delete', '删除图形')
    
    // 清除选择
    interactionState.value.selectedFeature = null
    
    emit?.('feature-delete', { feature, wkt })
  }

  /**
   * 清空所有绘制内容
   */
  const clearAllFeatures = () => {
    if (!drawSource.value) return

    // 记录历史
    addHistoryRecord('clear', '清空所有图形')
    
    // 清空数据源
    drawSource.value.clear()
    
    // 清除选择状态
    interactionState.value.selectedFeature = null
    
    emit?.('clear')
  }

  /**
   * 添加历史记录
   */
  const addHistoryRecord = (type: HistoryRecord['type'], description: string) => {
    if (!drawSource.value) return

    // 获取当前 WKT 数据
    const features = drawSource.value.getFeatures()
    const wktArray = WKTUtils.featuresToWKTArray(features)
    const wktSnapshot = WKTUtils.mergeWKTToCollection(wktArray)

    const record: HistoryRecord = {
      type,
      timestamp: Date.now(),
      wktSnapshot,
      description
    }

    // 如果当前不在历史末尾，清除后续记录
    if (historyIndex.value < history.value.length - 1) {
      history.value = history.value.slice(0, historyIndex.value + 1)
    }

    // 添加新记录
    history.value.push(record)
    historyIndex.value = history.value.length - 1

    // 限制历史记录大小
    if (history.value.length > maxHistorySize) {
      history.value.shift()
      historyIndex.value--
    }

    // 更新状态
    updateHistoryState()
  }

  /**
   * 撤销操作
   */
  const undo = () => {
    if (!canUndo.value) return

    historyIndex.value--
    restoreFromHistory()
    updateHistoryState()
  }

  /**
   * 重做操作
   */
  const redo = () => {
    if (!canRedo.value) return

    historyIndex.value++
    restoreFromHistory()
    updateHistoryState()
  }

  /**
   * 从历史记录恢复
   */
  const restoreFromHistory = () => {
    if (!drawSource.value || historyIndex.value < 0) return

    const record = history.value[historyIndex.value]
    if (!record) return

    // 清空当前数据
    drawSource.value.clear()

    // 恢复历史数据
    if (record.wktSnapshot) {
      const wktArray = WKTUtils.splitCollectionWKT(record.wktSnapshot)
      const features = WKTUtils.wktArrayToFeatures(wktArray)
      drawSource.value.addFeatures(features)
    }

    // 清除选择状态
    interactionState.value.selectedFeature = null
  }

  /**
   * 更新历史状态
   */
  const updateHistoryState = () => {
    interactionState.value.canUndo = historyIndex.value > 0
    interactionState.value.canRedo = historyIndex.value < history.value.length - 1
  }

  /**
   * 设置要素样式
   */
  const setFeatureStyle = (feature: Feature<Geometry>, state: 'drawing' | 'finished' | 'selected' | 'modifying') => {
    const geometryType = feature.getGeometry()?.getType() || 'Polygon'
    const style = StyleUtils.getStyleByGeometryType(geometryType, state)
    feature.setStyle(style)
  }

  /**
   * 切换绘制模式
   */
  const toggleDrawMode = (mode: DrawMode) => {
    if (interactionState.value.currentMode === mode) {
      // 如果是相同模式，切换到编辑模式
      activateEditMode()
    } else {
      // 激活新的绘制模式
      activateDrawMode(mode)
    }
  }

  // 计算属性
  const canUndo = computed(() => interactionState.value.canUndo)
  const canRedo = computed(() => interactionState.value.canRedo)
  const isDrawingMode = computed(() => interactionState.value.currentMode !== DrawMode.NONE)
  const hasSelectedFeature = computed(() => interactionState.value.selectedFeature !== null)
  const featureCount = computed(() => {
    if (!drawSource.value) return 0
    return drawSource.value.getFeatures().length
  })

  // 监听键盘事件
  const handleKeyDown = (event: KeyboardEvent) => {
    if (!map.value) return

    switch (event.key) {
      case 'Escape':
        // ESC 取消当前操作
        if (interactionState.value.isDrawing) {
          clearDrawInteraction()
          activateEditMode()
        } else {
          clearInteractions()
          interactionState.value.currentMode = DrawMode.NONE
        }
        break
      case 'Delete':
      case 'Backspace':
        // 删除选中要素
        if (hasSelectedFeature.value) {
          deleteSelectedFeature()
        }
        break
      case 'z':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault()
          if (event.shiftKey) {
            redo()
          } else {
            undo()
          }
        }
        break
    }
  }

  // 监听地图变化
  watch(map, (newMap, oldMap) => {
    if (oldMap) {
      // 移除键盘事件监听
      document.removeEventListener('keydown', handleKeyDown)
    }
    
    if (newMap) {
      // 添加键盘事件监听
      document.addEventListener('keydown', handleKeyDown)
      
      // 默认激活编辑模式
      nextTick(() => {
        activateEditMode()
      })
    }
  }, { immediate: true })

  return {
    // 状态
    interactionState: readonly(interactionState),
    
    // 交互方法
    activateDrawMode,
    activateEditMode,
    toggleDrawMode,
    clearInteractions,
    
    // 要素操作
    deleteSelectedFeature,
    clearAllFeatures,
    setFeatureStyle,
    
    // 历史操作
    undo,
    redo,
    
    // 计算属性
    canUndo,
    canRedo,
    isDrawingMode,
    hasSelectedFeature,
    featureCount,
    
    // 历史记录
    history: readonly(history),
    historyIndex: readonly(historyIndex)
  }
} 