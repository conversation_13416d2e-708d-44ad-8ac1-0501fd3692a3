# MapDraw 地图绘图组件

基于 OpenLayers 和 Vue 3 的地图绘图组件，支持绘制点、线、面等几何图形，输出 WKT 格式数据。

## 特性

- 🎨 **多种绘制模式**：支持点、线、面、圆的绘制
- 📝 **WKT 格式输出**：标准的 Well-Known Text 格式数据
- 🎯 **参考数据支持**：可传入参考图层数据进行对比绘制
- 🛠️ **丰富的编辑功能**：支持编辑、删除、撤销/重做等操作
- 🎭 **主题切换**：支持深色/浅色模式切换
- 📱 **响应式设计**：适配移动端和桌面端
- 🔧 **FormCreate 集成**：完美集成到表单设计器中

## 安装

组件已集成到项目中，无需额外安装。

## 基础用法

### 1. 核心地图组件

```vue
<template>
  <MapDrawCore
    v-model="wktData"
    :reference-data="referenceData"
    :allowed-modes="[DrawMode.POINT, DrawMode.LINE, DrawMode.POLYGON]"
    @draw-end="handleDrawEnd"
  />
</template>

<script setup>
import { ref } from 'vue'
import { MapDrawCore, DrawMode } from '@/components/MapDraw'

const wktData = ref('')
const referenceData = ref([
  'POINT(116.404 39.915)',
  'LINESTRING(116.404 39.915, 116.414 39.925)'
])

const handleDrawEnd = (event) => {
  console.log('绘制完成:', event.wkt)
}
</script>
```

### 2. 弹窗地图组件

```vue
<template>
  <div>
    <el-button @click="openMapDialog">打开地图绘图</el-button>
    
    <MapDrawDialog
      v-model:visible="dialogVisible"
      v-model:model-value="wktData"
      title="地图绘图"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { MapDrawDialog } from '@/components/MapDraw'

const dialogVisible = ref(false)
const wktData = ref('')

const openMapDialog = () => {
  dialogVisible.value = true
}

const handleConfirm = (wkt) => {
  console.log('确认绘制:', wkt)
}
</script>
```

### 3. FormCreate 表单集成

```vue
<template>
  <FormCreate v-model="formData" :rule="formRule" />
</template>

<script setup>
import { ref } from 'vue'

const formData = ref({})
const formRule = ref([
  {
    type: 'MapDraw',
    field: 'geometry',
    title: '地理位置',
    props: {
      dialogTitle: '选择地理位置',
      allowedModes: ['POINT', 'POLYGON'],
      mapCenter: [116.404, 39.915],
      zoom: 12
    }
  }
])
</script>
```

## API 文档

### MapDrawCore 组件

#### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | string | '' | 绑定的 WKT 数据 |
| referenceData | string[] | [] | 参考图层数据（WKT 格式） |
| referenceStyles | ReferenceStyle[] | [] | 参考图层样式配置 |
| showToolbar | boolean | true | 是否显示工具栏 |
| allowedModes | DrawMode[] | [POINT, LINE, POLYGON, CIRCLE] | 允许的绘制模式 |
| center | [number, number] | [116.404, 39.915] | 地图中心点 |
| zoom | number | 10 | 地图缩放级别 |
| projection | string | 'EPSG:3857' | 坐标系 |
| readonly | boolean | false | 是否只读 |
| height | string \| number | '400px' | 地图高度 |
| width | string \| number | '100%' | 地图宽度 |

#### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | wkt: string | WKT 数据更新 |
| draw-start | event: DrawStartEvent | 开始绘制 |
| draw-end | event: DrawEndEvent | 绘制完成 |
| feature-select | event: FeatureSelectEvent | 要素选中 |
| feature-modify | event: FeatureModifyEvent | 要素修改 |
| feature-delete | event: FeatureDeleteEvent | 要素删除 |
| clear | - | 清空所有要素 |

#### Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getMap | - | Map | 获取地图实例 |
| getWKTData | - | string | 获取 WKT 数据 |
| setWKTData | wkt: string | - | 设置 WKT 数据 |
| clearData | - | - | 清空数据 |
| activateDrawMode | mode: DrawMode | - | 激活绘制模式 |
| activateEditMode | - | - | 激活编辑模式 |
| undo | - | - | 撤销操作 |
| redo | - | - | 重做操作 |

### MapDrawDialog 组件

继承 MapDrawCore 的所有属性和事件，额外增加：

#### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| visible | boolean | false | 弹窗是否可见 |
| title | string | '地图绘图' | 弹窗标题 |
| width | string \| number | '80%' | 弹窗宽度 |
| height | string \| number | '70%' | 弹窗高度 |
| mapHeight | string \| number | '500px' | 地图容器高度 |
| mapWidth | string \| number | '100%' | 地图容器宽度 |
| confirmButtonText | string | '确定' | 确认按钮文字 |
| cancelButtonText | string | '取消' | 取消按钮文字 |
| showDataPreview | boolean | true | 显示数据预览 |
| showClearButton | boolean | true | 显示清空按钮 |
| showImportButton | boolean | true | 显示导入按钮 |
| showExportButton | boolean | true | 显示导出按钮 |
| showFullscreenButton | boolean | true | 显示全屏按钮 |
| showHelpButton | boolean | true | 显示帮助按钮 |

#### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:visible | visible: boolean | 弹窗显示状态更新 |
| confirm | wkt: string | 确认操作 |
| cancel | - | 取消操作 |

### FormCreate MapDraw 组件

#### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | string | '' | 绑定的 WKT 数据 |
| readonly | boolean | false | 是否只读 |
| allowEdit | boolean | true | 允许编辑 |
| allowClear | boolean | true | 允许清空 |
| allowPreview | boolean | true | 允许预览 |
| showPreviewData | boolean | false | 显示预览数据 |
| dialogTitle | string | '地图绘图' | 弹窗标题 |
| dialogWidth | string \| number | '85%' | 弹窗宽度 |
| dialogHeight | string \| number | '75%' | 弹窗高度 |

## 类型定义

### DrawMode 枚举

```typescript
enum DrawMode {
  POINT = 'Point',
  LINE = 'LineString', 
  POLYGON = 'Polygon',
  CIRCLE = 'Circle'
}
```

### 事件类型

```typescript
interface DrawStartEvent {
  mode: DrawMode
  feature: any
}

interface DrawEndEvent {
  mode: DrawMode
  feature: any
  wkt: string
}

interface FeatureSelectEvent {
  feature: any | null
  wkt?: string
}

interface FeatureModifyEvent {
  feature: any
  wkt: string
}

interface FeatureDeleteEvent {
  feature: any
  wkt: string
}
```

### 样式配置

```typescript
interface ReferenceStyle {
  stroke?: {
    color?: string
    width?: number
    lineDash?: number[]
  }
  fill?: {
    color?: string
  }
  circle?: {
    radius?: number
    stroke?: { color?: string; width?: number }
    fill?: { color?: string }
  }
  text?: {
    font?: string
    fill?: { color?: string }
    stroke?: { color?: string; width?: number }
    offsetX?: number
    offsetY?: number
  }
}
```

## 工具函数

### WKTUtils

```typescript
// WKT 格式验证
WKTUtils.isValidWKT(wkt: string): boolean

// WKT 转 Feature
WKTUtils.wktToFeature(wkt: string): Feature | null

// Feature 转 WKT
WKTUtils.featureToWKT(feature: Feature): string

// WKT 转 GeoJSON
WKTUtils.wktToGeoJSON(wkt: string): GeoJSON | null

// GeoJSON 转 WKT
WKTUtils.geoJSONToWKT(geojson: GeoJSON): string

// 分割几何集合
WKTUtils.splitCollectionWKT(wkt: string): string[]

// 合并为几何集合
WKTUtils.mergeToCollectionWKT(wktArray: string[]): string
```

### StyleUtils

```typescript
// 创建样式
StyleUtils.createStyle(config: StyleConfig): Style

// 创建主题样式
StyleUtils.createThemeStyle(theme: 'light' | 'dark'): Style

// 获取几何类型样式
StyleUtils.getGeometryTypeStyle(geometryType: string): Style
```

### GeometryUtils

```typescript
// 计算面积
GeometryUtils.calculateArea(geometry: Geometry): number

// 计算长度
GeometryUtils.calculateLength(geometry: Geometry): number

// 坐标转换
GeometryUtils.transformCoordinates(coords: number[], fromProj: string, toProj: string): number[]

// 几何验证
GeometryUtils.isValidGeometry(geometry: Geometry): boolean
```

## 快捷键

| 快捷键 | 功能 |
|--------|------|
| 1 | 激活点绘制模式 |
| 2 | 激活线绘制模式 |
| 3 | 激活面绘制模式 |
| 4 | 激活圆绘制模式 |
| 0 | 激活选择编辑模式 |
| Ctrl+Z | 撤销操作 |
| Ctrl+Shift+Z | 重做操作 |
| Delete | 删除选中要素 |
| Esc | 取消当前操作 |

## 主题定制

组件支持 CSS 变量定制主题：

```css
:root {
  --map-draw-bg-color: #ffffff;
  --map-draw-border-color: #dcdfe6;
  --map-draw-text-color: #303133;
  /* 更多变量... */
}

[data-theme="dark"] {
  --map-draw-bg-color: #1f1f1f;
  --map-draw-border-color: #414243;
  --map-draw-text-color: #e5eaf3;
  /* 更多变量... */
}
```

## 常见问题

### Q: 如何自定义地图底图？

A: 可以通过修改 MapDrawCore 组件中的底图配置：

```javascript
// 在 initializeMap 方法中替换底图
const baseLayer = new TileLayer({
  source: new XYZ({
    url: 'https://your-tile-server/{z}/{x}/{y}.png'
  })
})
```

### Q: 如何处理大量参考数据？

A: 建议对大量参考数据进行分页加载或使用聚合显示：

```javascript
// 分批加载参考数据
const loadReferenceDataBatch = (data, batchSize = 100) => {
  // 实现分批加载逻辑
}
```

### Q: 如何自定义绘制样式？

A: 可以通过 referenceStyles 属性传入自定义样式：

```javascript
const customStyles = [{
  stroke: { color: '#ff0000', width: 2 },
  fill: { color: 'rgba(255, 0, 0, 0.1)' }
}]
```

## 更新日志

### v1.0.0 (2024-12-25)

- ✨ 初始版本发布
- 🎨 支持点、线、面、圆的绘制
- 📝 WKT 格式数据输出
- 🛠️ 完整的编辑功能
- 🎭 主题切换支持
- 🔧 FormCreate 集成

## 许可证

MIT License 